"use client";
import React from "react";
import { StickyScroll } from "./ui/sticky-scroll-reveal";


const content = [
  {
    title: "🚀 Introducing ROS: Revolutionizing Restaurant Operations",
    description:
      "In the fast-paced world of hospitality, efficiency and adaptability are paramount. MenuFlow's Restaurant Operating System (ROS) is designed to streamline restaurant operations, offering a seamless integration of technology to enhance both customer experience and backend management.",
    content: (
      <div className="flex h-full w-full items-center justify-center bg-[linear-gradient(to_bottom_right,#06b6d4,#10b981)] text-white p-6 font-bold text-xl">
        <div className="flex flex-col items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          <span>Restaurant Operating System</span>
        </div>
      </div>
    ),
  },
  {
    title: "Key Benefits",
    description:
      "• Enhanced Efficiency: Automate order processing and table management.\n• Real-Time Analytics: Monitor traffic and sales data to make informed decisions.\n• Scalable Integration: Easily connect with existing POS systems and third-party applications.",
    content: (
      <div className="flex h-full w-full items-center justify-center bg-[linear-gradient(to_bottom_right,#ec4899,#6366f1)] text-white p-6 font-bold text-xl">
        <div className="flex flex-col items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Key Benefits</span>
        </div>
      </div>
    ),
  },
  {
    title: "🛠️ Developer Documentation: Implementing ROS",
    description:
      "For developers and tech-savvy restaurant owners, here's a detailed guide on integrating and utilizing ROS within your establishment. ROS is built on a modular architecture using Python FastAPI for the backend and React for the frontend. This ensures scalability and ease of maintenance.",
    content: (
      <div className="flex h-full w-full items-center justify-center bg-[linear-gradient(to_bottom_right,#f97316,#eab308)] text-white p-6 font-bold text-xl">
        <div className="flex flex-col items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
          </svg>
          <span>Developer Documentation</span>
        </div>
      </div>
    ),
  },
  {
    title: "Core Modules",
    description:
      "• Dynamic Pricing Engine: Adjusts menu prices based on real-time demand and predefined rules.\n• Traffic Monitoring: Tracks table occupancy and customer flow to optimize service.\n• Order Management: Handles order placement, modifications, and kitchen communication.\n• Notification System: Alerts staff of new orders, special requests, and system updates.",
    content: (
      <div className="flex h-full w-full items-center justify-center bg-[linear-gradient(to_bottom_right,#8b5cf6,#3b82f6)] text-white p-6 font-bold text-xl">
        <div className="flex flex-col items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
          </svg>
          <span>Core Modules</span>
        </div>
      </div>
    ),
  },
  {
    title: "Integration Steps",
    description:
      "• API Endpoints: Utilize RESTful APIs to connect ROS with existing systems.\n• Webhooks: Set up webhooks for real-time updates and notifications.\n• Authentication: Implement OAuth 2.0 for secure access control.",
    content: (
      <div className="flex h-full w-full items-center justify-center bg-[linear-gradient(to_bottom_right,#06b6d4,#10b981)] text-white p-6 font-bold text-xl">
        <div className="flex flex-col items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <span>Integration Steps</span>
        </div>
      </div>
    ),
  },
  {
    title: "📈 Real-World Impact",
    description:
      "Restaurants implementing ROS have reported:\n• 20% Increase in Table Turnover: Faster order processing leads to quicker table availability.\n• 15% Boost in Sales: Dynamic pricing and upselling features enhance revenue.\n• Improved Customer Satisfaction: Streamlined operations result in better service quality.",
    content: (
      <div className="flex h-full w-full items-center justify-center bg-[linear-gradient(to_bottom_right,#ec4899,#6366f1)] text-white p-6 font-bold text-xl">
        <div className="flex flex-col items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <span>Real-World Impact</span>
        </div>
      </div>
    ),
  },
  {
    title: "📚 Additional Resources",
    description:
      "For a deeper dive into ROS integration and best practices, consider exploring the following:\n• ROS Best Practices\n• ROS 2 Documentation",
    content: (
      <div className="flex h-full w-full items-center justify-center bg-[linear-gradient(to_bottom_right,#f97316,#eab308)] text-white p-6 font-bold text-xl">
        <div className="flex flex-col items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
          <span>Additional Resources</span>
        </div>
      </div>
    ),
  },
];
export default function StickyScrollRevealDemo() {
  return (
    <div className="w-full h-full">
      <StickyScroll content={content} />
    </div>
  );
}
