'use client';

import { motion } from 'framer-motion';
import { 
  FiShoppingBag, 
  FiTool, 
  FiShoppingCart, 
  FiSettings, 
  FiTrendingUp,
  FiUsers,
  FiDollarSign,
  FiBarChart
} from 'react-icons/fi';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

const useCases = [
  {
    icon: FiShoppingBag,
    industry: "Retail Businesses",
    title: "Boutique Fashion Store Increases Profits by 35%",
    challenge: "<PERSON>'s boutique struggled with inventory management, often running out of popular items while overstocking others. She couldn't predict seasonal trends or understand which products were truly profitable.",
    solution: "SME Analytica's AI analyzed 2 years of sales data, weather patterns, and local events to predict demand. The system identified that certain colors sold 40% better during specific months and that accessories had 3x higher margins than clothing.",
    results: [
      "35% increase in overall profitability",
      "50% reduction in overstock situations",
      "25% improvement in inventory turnover",
      "Eliminated stockouts of popular items"
    ],
    metrics: {
      before: "Manual inventory tracking, 15% profit margin",
      after: "AI-driven predictions, 25% profit margin"
    },
    keywords: "retail analytics small business, fashion store inventory management"
  },
  {
    icon: FiTool,
    industry: "Service-Based Businesses",
    title: "HVAC Company Optimizes Scheduling & Pricing",
    challenge: "Mike's HVAC company was losing money on certain jobs and couldn't predict busy seasons. Customer churn was high, and he didn't know which services were most profitable.",
    solution: "SME Analytica analyzed service history, seasonal patterns, and customer behavior. The AI identified that maintenance contracts were 5x more profitable than emergency repairs and predicted peak demand periods.",
    results: [
      "40% increase in maintenance contract sales",
      "30% improvement in technician utilization",
      "25% reduction in customer churn",
      "20% increase in average job value"
    ],
    metrics: {
      before: "Reactive scheduling, 12% profit margin",
      after: "Predictive scheduling, 18% profit margin"
    },
    keywords: "service business analytics, HVAC company optimization"
  },
  {
    icon: FiShoppingCart,
    industry: "E-commerce Stores",
    title: "Online Pet Store Doubles Revenue with AI Insights",
    challenge: "Lisa's online pet store had inconsistent sales and high cart abandonment rates. She couldn't identify which marketing channels worked or predict which products would sell.",
    solution: "SME Analytica's AI analyzed customer behavior, seasonal pet trends, and marketing performance. It discovered that customers buying premium food were 3x more likely to purchase accessories within 30 days.",
    results: [
      "100% increase in monthly revenue",
      "45% reduction in cart abandonment",
      "60% improvement in customer lifetime value",
      "35% increase in repeat purchases"
    ],
    metrics: {
      before: "Random product recommendations, 8% conversion rate",
      after: "AI-powered personalization, 15% conversion rate"
    },
    keywords: "e-commerce analytics small business, online store optimization"
  },
  {
    icon: FiSettings,
    industry: "Small Manufacturing",
    title: "Custom Furniture Maker Streamlines Operations",
    challenge: "Tom's custom furniture business struggled with material waste, unpredictable delivery times, and pricing jobs accurately. He often underestimated costs and lost money on projects.",
    solution: "SME Analytica analyzed production data, material costs, and project timelines. The AI identified patterns in material usage and predicted accurate project completion times based on complexity.",
    results: [
      "30% reduction in material waste",
      "50% improvement in delivery time accuracy",
      "25% increase in project profitability",
      "40% faster quote generation"
    ],
    metrics: {
      before: "Manual cost estimation, 10% profit margin",
      after: "AI-driven pricing, 16% profit margin"
    },
    keywords: "manufacturing analytics SME, custom furniture business optimization"
  },
  {
    icon: FiTrendingUp,
    industry: "Marketing Agencies",
    title: "Digital Agency Improves Client ROI by 200%",
    challenge: "Rachel's digital marketing agency couldn't prove ROI to clients and struggled to optimize campaigns across different platforms. Client retention was low due to unclear results.",
    solution: "SME Analytica integrated with all major advertising platforms and CRM systems. The AI identified which campaigns, keywords, and audiences delivered the best ROI for each client industry.",
    results: [
      "200% improvement in average client ROI",
      "80% increase in client retention rate",
      "50% reduction in campaign optimization time",
      "35% increase in agency profit margins"
    ],
    metrics: {
      before: "Manual reporting, 60% client retention",
      after: "AI-powered insights, 95% client retention"
    },
    keywords: "marketing agency analytics, digital marketing ROI optimization"
  },
  {
    icon: FiUsers,
    industry: "Professional Services",
    title: "Accounting Firm Predicts Client Needs & Grows Revenue",
    challenge: "David's accounting firm was reactive, only helping clients during tax season. He couldn't identify which clients needed additional services or predict cash flow issues.",
    solution: "SME Analytica analyzed client financial data patterns and identified early warning signs of cash flow problems. The AI also predicted which clients would benefit from specific services.",
    results: [
      "45% increase in year-round revenue",
      "60% improvement in client satisfaction",
      "30% reduction in client churn",
      "50% increase in additional service sales"
    ],
    metrics: {
      before: "Seasonal revenue spikes, reactive service",
      after: "Steady revenue growth, proactive client care"
    },
    keywords: "accounting firm analytics, professional services optimization"
  }
];

export default function UseCasesClient() {
  return (
    <div className="min-h-screen bg-[#171f31]">
      <Navbar />
      
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-6">
        <div className="container mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h1 className="text-5xl md:text-6xl font-bold text-[#d5dce2] mb-6">
              Real SMEs, Real{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#11a5e8] to-[#5f7790]">
                Success Stories
              </span>
            </h1>
            <p className="text-xl text-[#d5dce2]/80 max-w-3xl mx-auto">
              See how businesses just like yours are using AI analytics to solve real challenges, 
              increase profitability, and accelerate growth across different industries.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Use Cases */}
      <section className="pb-20 px-6">
        <div className="container mx-auto max-w-7xl">
          <div className="space-y-16">
            {useCases.map((useCase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-[#1e2a3e] rounded-2xl p-8 border border-[#2a3441]"
              >
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Industry & Title */}
                  <div className="lg:col-span-3">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="p-3 bg-[#11a5e8]/10 rounded-xl">
                        <useCase.icon className="w-8 h-8 text-[#11a5e8]" />
                      </div>
                      <div>
                        <div className="text-[#11a5e8] font-semibold text-lg">{useCase.industry}</div>
                        <h3 className="text-3xl font-bold text-[#d5dce2]">{useCase.title}</h3>
                      </div>
                    </div>
                  </div>

                  {/* Challenge */}
                  <div>
                    <h4 className="text-xl font-semibold text-[#d5dce2] mb-4 flex items-center gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full" />
                      The Challenge
                    </h4>
                    <p className="text-[#d5dce2]/70">{useCase.challenge}</p>
                  </div>

                  {/* Solution */}
                  <div>
                    <h4 className="text-xl font-semibold text-[#d5dce2] mb-4 flex items-center gap-2">
                      <div className="w-3 h-3 bg-[#11a5e8] rounded-full" />
                      The Solution
                    </h4>
                    <p className="text-[#d5dce2]/70">{useCase.solution}</p>
                  </div>

                  {/* Results */}
                  <div>
                    <h4 className="text-xl font-semibold text-[#d5dce2] mb-4 flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full" />
                      The Results
                    </h4>
                    <ul className="space-y-2">
                      {useCase.results.map((result, resultIndex) => (
                        <li key={resultIndex} className="flex items-start gap-3">
                          <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-[#d5dce2]/80">{result}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Metrics Comparison */}
                <div className="mt-8 pt-8 border-t border-[#2a3441]">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-red-500/10 rounded-xl p-6 border border-red-500/20">
                      <h5 className="text-lg font-semibold text-red-400 mb-2">Before SME Analytica</h5>
                      <p className="text-[#d5dce2]/70">{useCase.metrics.before}</p>
                    </div>
                    <div className="bg-green-500/10 rounded-xl p-6 border border-green-500/20">
                      <h5 className="text-lg font-semibold text-green-400 mb-2">After SME Analytica</h5>
                      <p className="text-[#d5dce2]/70">{useCase.metrics.after}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Industry Stats */}
      <section className="py-20 px-6 bg-[#1e2a3e]/50">
        <div className="container mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-[#d5dce2] mb-6">
              Proven Results Across Industries
            </h2>
            <p className="text-xl text-[#d5dce2]/80">
              Average improvements our SME clients see within 6 months
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { icon: FiDollarSign, value: '32%', label: 'Profit Increase' },
              { icon: FiTrendingUp, value: '45%', label: 'Revenue Growth' },
              { icon: FiBarChart, value: '60%', label: 'Better Forecasting' },
              { icon: FiUsers, value: '25%', label: 'Customer Retention' }
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2 }}
              >
                <div className="p-4 bg-[#11a5e8]/10 rounded-xl w-fit mx-auto mb-4">
                  <stat.icon className="w-8 h-8 text-[#11a5e8]" />
                </div>
                <div className="text-4xl font-bold text-[#11a5e8] mb-2">{stat.value}</div>
                <div className="text-[#d5dce2]/80">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6">
        <div className="container mx-auto max-w-4xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-gradient-to-r from-[#11a5e8]/10 to-[#5f7790]/10 rounded-2xl p-12 border border-[#2a3441]"
          >
            <h2 className="text-4xl font-bold text-[#d5dce2] mb-6">
              Ready to Write Your Success Story?
            </h2>
            <p className="text-xl text-[#d5dce2]/80 mb-8">
              Join hundreds of SMEs who are already using AI analytics to transform their businesses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="https://calendly.com/smeanalytica/30min"
                target="_blank"
                rel="noopener noreferrer"
                className="px-8 py-4 bg-[#11a5e8] text-white rounded-full text-lg font-medium hover:bg-[#0e8bc7] transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Schedule Your Demo
              </motion.a>
              <motion.a
                href="/pricing"
                className="px-8 py-4 bg-transparent text-[#d5dce2] rounded-full text-lg font-medium border-2 border-[#5f7790] hover:bg-[#5f7790] transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                View Pricing
              </motion.a>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
} 