import type { Metadata } from 'next';
import ContactClient from './ContactClient';

export const metadata: Metadata = {
  title: 'Contact SME Analytica | Get in Touch with Our Team',
  description: 'Contact SME Analytica for demos, support, or questions about AI analytics for your small business. Multiple ways to reach our team of experts.',
  keywords: [
    'contact SME Analytica',
    'small business analytics support',
    'AI analytics demo',
    'business intelligence consultation',
    'SME analytics contact',
    'get in touch'
  ],
  openGraph: {
    title: 'Contact SME Analytica | Get in Touch',
    description: 'Ready to transform your business with AI analytics? Contact our team for a personalized demo and consultation.',
    url: 'https://smeanalytica.dev/contact',
  },
};

export default function ContactPage() {
  return <ContactClient />;
} 