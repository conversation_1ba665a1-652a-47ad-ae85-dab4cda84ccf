'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { FiCalendar, FiClock, FiArrowRight, FiSearch, FiTag } from 'react-icons/fi';

// Blog categories for content pillars
const categories = [
  'All',
  'Cash Flow Management',
  'Sales Analytics',
  'Inventory Optimization',
  'Customer Insights',
  'Financial Planning',
  'Business Intelligence',
  'Industry Trends'
];

// Featured blog posts with SEO-focused titles and content
const blogPosts = [
  {
    id: 1,
    title: "5 Cash Flow Prediction Strategies Every Small Business Owner Must Know",
    excerpt: "Learn how AI-powered analytics can help predict cash flow gaps before they happen, with real examples from successful SMEs.",
    category: "Cash Flow Management",
    author: "<PERSON>",
    date: "2024-01-15",
    readTime: "8 min read",
    image: "/images/blog/cash-flow-prediction.jpg",
    slug: "cash-flow-prediction-strategies-small-business",
    featured: true,
    tags: ["cash flow", "financial planning", "AI analytics", "small business"]
  },
  {
    id: 2,
    title: "How Retail Stores Increase Sales by 40% Using Customer Behavior Analytics",
    excerpt: "Discover the data-driven strategies that helped 200+ retail SMEs optimize their sales performance and customer experience.",
    category: "Sales Analytics",
    author: "Michael Rodriguez",
    date: "2024-01-12",
    readTime: "6 min read",
    image: "/images/blog/retail-analytics.jpg",
    slug: "retail-customer-behavior-analytics-increase-sales",
    featured: true,
    tags: ["retail analytics", "customer behavior", "sales optimization", "SME growth"]
  },
  {
    id: 3,
    title: "Small Business Inventory Management: AI vs Traditional Methods",
    excerpt: "Compare traditional inventory management with AI-powered solutions and see why 85% of SMEs are making the switch.",
    category: "Inventory Optimization",
    author: "Jennifer Park",
    date: "2024-01-10",
    readTime: "7 min read",
    image: "/images/blog/inventory-management.jpg",
    slug: "ai-inventory-management-small-business-comparison",
    featured: false,
    tags: ["inventory management", "AI automation", "cost reduction", "efficiency"]
  },
  {
    id: 4,
    title: "Restaurant Analytics: How to Reduce Food Waste by 30% in 90 Days",
    excerpt: "Step-by-step guide to implementing analytics-driven food waste reduction strategies that save thousands monthly.",
    category: "Industry Trends",
    author: "David Kim",
    date: "2024-01-08",
    readTime: "9 min read",
    image: "/images/blog/restaurant-waste-reduction.jpg",
    slug: "restaurant-food-waste-reduction-analytics",
    featured: false,
    tags: ["restaurant analytics", "food waste", "cost savings", "sustainability"]
  },
  {
    id: 5,
    title: "E-commerce Analytics for Small Businesses: Complete 2024 Guide",
    excerpt: "Everything you need to know about e-commerce analytics, from setup to advanced insights that drive growth.",
    category: "Business Intelligence",
    author: "Lisa Thompson",
    date: "2024-01-05",
    readTime: "12 min read",
    image: "/images/blog/ecommerce-analytics.jpg",
    slug: "ecommerce-analytics-small-business-guide-2024",
    featured: false,
    tags: ["e-commerce", "online analytics", "conversion optimization", "digital marketing"]
  },
  {
    id: 6,
    title: "Customer Lifetime Value: How SMEs Calculate and Improve CLV",
    excerpt: "Master customer lifetime value calculations and strategies to increase customer retention and profitability.",
    category: "Customer Insights",
    author: "Robert Johnson",
    date: "2024-01-03",
    readTime: "10 min read",
    image: "/images/blog/customer-lifetime-value.jpg",
    slug: "customer-lifetime-value-calculation-improvement-sme",
    featured: false,
    tags: ["customer retention", "CLV", "customer analytics", "profitability"]
  }
];

export default function BlogPage() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredPosts = blogPosts.filter(post => {
    const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory;
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  const featuredPosts = blogPosts.filter(post => post.featured);

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#171f31]">
        {/* Hero Section */}
        <section className="pt-32 pb-16 px-6">
          <div className="max-w-6xl mx-auto text-center">
            <motion.h1
              className="text-4xl md:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#11a5e8] to-[#5f7790] mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              SME Analytics Insights
            </motion.h1>
            <motion.p
              className="text-xl text-[#d5dce2]/80 mb-8 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Expert insights, practical guides, and success stories to help small businesses 
              leverage data analytics for growth, efficiency, and competitive advantage.
            </motion.p>
            
            {/* Search Bar */}
            <motion.div
              className="max-w-md mx-auto relative"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[#d5dce2]/60" />
              <input
                type="text"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-[#1e2a3f] border border-[#5f7790]/30 rounded-full text-[#d5dce2] placeholder-[#d5dce2]/60 focus:outline-none focus:border-[#11a5e8] transition-colors"
              />
            </motion.div>
                </div>
        </section>

        {/* Category Filter */}
        <section className="px-6 mb-12">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-wrap justify-center gap-3">
              {categories.map((category, index) => (
                <motion.button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                    selectedCategory === category
                      ? 'bg-[#11a5e8] text-white'
                      : 'bg-[#1e2a3f] text-[#d5dce2]/80 hover:bg-[#5f7790]/20 border border-[#5f7790]/30'
                  }`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {category}
                </motion.button>
              ))}
            </div>
          </div>
        </section>

        {/* Featured Articles */}
        {selectedCategory === 'All' && (
          <section className="px-6 mb-16">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl font-bold text-[#d5dce2] mb-8 text-center">Featured Articles</h2>
              <div className="grid md:grid-cols-2 gap-8">
                {featuredPosts.map((post, index) => (
                  <motion.article
                    key={post.id}
                    className="bg-[#1e2a3f] rounded-2xl overflow-hidden border border-[#5f7790]/20 hover:border-[#11a5e8]/40 transition-all duration-300 group"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.2 }}
                    whileHover={{ y: -5 }}
                  >
                    <div className="relative h-48 bg-gradient-to-br from-[#11a5e8]/20 to-[#5f7790]/20">
                      <div className="absolute inset-0 bg-gradient-to-t from-[#1e2a3f]/80 to-transparent" />
                      <div className="absolute bottom-4 left-4">
                        <span className="px-3 py-1 bg-[#11a5e8] text-white text-xs font-medium rounded-full">
                          Featured
                        </span>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex items-center gap-4 text-sm text-[#d5dce2]/60 mb-3">
                        <div className="flex items-center gap-1">
                          <FiCalendar className="w-4 h-4" />
                          {new Date(post.date).toLocaleDateString('en-US', { 
                            month: 'short', 
                            day: 'numeric', 
                            year: 'numeric' 
                          })}
                        </div>
                        <div className="flex items-center gap-1">
                          <FiClock className="w-4 h-4" />
                          {post.readTime}
                        </div>
                      </div>
                      <h3 className="text-xl font-bold text-[#d5dce2] mb-3 group-hover:text-[#11a5e8] transition-colors">
                        {post.title}
                      </h3>
                      <p className="text-[#d5dce2]/80 mb-4 line-clamp-3">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <FiTag className="w-4 h-4 text-[#11a5e8]" />
                          <span className="text-sm text-[#11a5e8]">{post.category}</span>
                        </div>
                        <Link
                          href={`/blog/${post.slug}`}
                          className="flex items-center gap-2 text-[#11a5e8] hover:text-[#11a5e8]/80 transition-colors"
                        >
                          Read More <FiArrowRight className="w-4 h-4" />
                        </Link>
        </div>
        </div>
                  </motion.article>
                ))}
          </div>
        </div>
          </section>
        )}

        {/* All Articles Grid */}
        <section className="px-6 pb-16">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-[#d5dce2] mb-8 text-center">
              {selectedCategory === 'All' ? 'Latest Articles' : `${selectedCategory} Articles`}
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.map((post, index) => (
                <motion.article
                  key={post.id}
                  className="bg-[#1e2a3f] rounded-xl overflow-hidden border border-[#5f7790]/20 hover:border-[#11a5e8]/40 transition-all duration-300 group"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ y: -3 }}
                >
                  <div className="relative h-40 bg-gradient-to-br from-[#11a5e8]/10 to-[#5f7790]/10">
                    <div className="absolute top-3 left-3">
                      <span className="px-2 py-1 bg-[#1e2a3f]/80 text-[#11a5e8] text-xs font-medium rounded">
                        {post.category}
                      </span>
                    </div>
                  </div>
                  <div className="p-5">
                    <div className="flex items-center gap-3 text-xs text-[#d5dce2]/60 mb-3">
                      <span>{new Date(post.date).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric' 
                      })}</span>
                      <span>•</span>
                      <span>{post.readTime}</span>
                    </div>
                    <h3 className="text-lg font-bold text-[#d5dce2] mb-3 group-hover:text-[#11a5e8] transition-colors line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-[#d5dce2]/70 text-sm mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-[#d5dce2]/60">By {post.author}</span>
                      <Link
                        href={`/blog/${post.slug}`}
                        className="text-[#11a5e8] hover:text-[#11a5e8]/80 transition-colors"
                      >
                        <FiArrowRight className="w-4 h-4" />
                      </Link>
                    </div>
                  </div>
                </motion.article>
              ))}
                </div>

            {filteredPosts.length === 0 && (
              <div className="text-center py-12">
                <p className="text-[#d5dce2]/60 text-lg">
                  No articles found matching your search criteria.
                </p>
              </div>
            )}
          </div>
        </section>

        {/* Newsletter Signup */}
        <section className="px-6 pb-16">
          <div className="max-w-4xl mx-auto">
            <motion.div
              className="bg-gradient-to-r from-[#11a5e8]/10 to-[#5f7790]/10 rounded-2xl p-8 text-center border border-[#11a5e8]/20"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h3 className="text-2xl font-bold text-[#d5dce2] mb-4">
                Stay Updated with SME Analytics Insights
              </h3>
              <p className="text-[#d5dce2]/80 mb-6">
                Get weekly insights, case studies, and actionable tips delivered to your inbox.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-3 bg-[#1e2a3f] border border-[#5f7790]/30 rounded-lg text-[#d5dce2] placeholder-[#d5dce2]/60 focus:outline-none focus:border-[#11a5e8]"
                />
                <button className="px-6 py-3 bg-[#11a5e8] text-white rounded-lg font-medium hover:bg-[#11a5e8]/90 transition-colors">
                  Subscribe
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
}