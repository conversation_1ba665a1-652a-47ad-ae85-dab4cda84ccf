import { Metadata } from 'next';
import CashFlowPostClient from './CashFlowPostClient';

export const metadata: Metadata = {
  title: '5 Cash Flow Prediction Strategies Every Small Business Owner Must Know | SME Analytica',
  description: 'Learn how AI-powered analytics can help predict cash flow gaps before they happen, with real examples from successful SMEs. Improve your business financial planning today.',
  keywords: [
    'cash flow prediction',
    'small business cash flow',
    'financial planning SME',
    'AI analytics cash flow',
    'business financial forecasting',
    'cash flow management strategies',
    'small business financial planning',
    'predictive analytics business'
  ],
  openGraph: {
    title: '5 Cash Flow Prediction Strategies Every Small Business Owner Must Know',
    description: 'Learn how AI-powered analytics can help predict cash flow gaps before they happen, with real examples from successful SMEs.',
    type: 'article',
    publishedTime: '2024-01-15T00:00:00.000Z',
    authors: ['<PERSON>'],
    tags: ['cash flow', 'financial planning', 'AI analytics', 'small business'],
  },
  twitter: {
    card: 'summary_large_image',
    title: '5 Cash Flow Prediction Strategies Every Small Business Owner Must Know',
    description: 'Learn how AI-powered analytics can help predict cash flow gaps before they happen, with real examples from successful SMEs.',
  },
};

export default function CashFlowPredictionPost() {
  return <CashFlowPostClient />;
} 