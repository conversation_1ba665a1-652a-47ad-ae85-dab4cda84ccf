# TODO List

## Database & Subscription System

### Separate Subscription Tiers by Product
**Priority: High**

Currently, all products are sharing the same subscription tier system, but each product has different pricing structures:

- **Mobile App**: Starts at $9.99
- **ROS (Restaurant Operating System)**: Starts at $39.99  
- **Connecto**: Starts at $14.99

**Required Changes:**
1. Separate subscription tables/schemas in the database for each product
2. Create product-specific pricing tiers and plans
3. Update subscription logic to handle different products independently
4. Ensure billing system can handle multiple product subscriptions per user
5. Update frontend subscription components for each product

**Affected Areas:**
- Database schema (Supabase)
- Subscription API endpoints
- Frontend subscription components in:
  - `/mobile/` - Mobile app subscriptions
  - `/frontend/menu-flow-dynamo/` - ROS subscriptions  
  - Future Connecto application

**Files to Review:**
- Database migration files
- Subscription service logic
- API endpoints handling subscriptions
- Frontend subscription forms and pricing displays

---

## Database Issues

### Missing Customer Feedback Table
**Priority: ✅ RESOLVED**

~~The customer feedback system is fully implemented in the frontend menu-flow-dynamo, but the `customer_feedback` table **DOES NOT EXIST** in the database schema.~~

**FIXED**: Created `customer_feedback` table in ROS database with:

1. ✅ **Frontend Implementation**: Complete feedback collection system with ratings and sentiment analysis
2. ✅ **Database Table**: `customer_feedback` table created in ROS Supabase database  
3. ✅ **Data Storage**: Customer feedback will now be properly stored and accessible
4. ✅ **AI Training Data**: Feedback data available for AI improvements and training

**Current Frontend Features (already implemented):**
- Customer feedback form with food/service/app ratings (1-5 stars)
- Comment collection with sentiment analysis
- Email collection (optional)
- Integration with order completion flow
- Automatic feedback prompts after order delivery/completion

**Missing Backend:**
- Database table to store feedback data
- Proper schema with RLS policies
- Integration with AI training pipelines

**Required Actions:**
1. Create `customer_feedback` table migration in `frontend/menu-flow-dynamo/supabase/migrations/`
2. Apply migration to production database
3. Verify feedback data is being stored correctly
4. Connect stored feedback data to AI training systems

**Business Impact:**
- 🔴 **Lost Customer Insights**: All valuable customer feedback is being discarded
- 🔴 **No AI Improvement Data**: Missing critical data for restaurant AI optimization
- 🔴 **Poor Customer Experience**: Customers think their feedback is being recorded but it's not

---

## Integration Requirements

### Connect Customer Feedback to SME Analytica Sentiment Analysis
**Priority: HIGH** 

Customer feedback from menu-flow-dynamo (restaurant orders) should feed directly into the main SME Analytica sentiment analysis engine for comprehensive business intelligence.

**Current State:**
- ✅ Customer feedback collection system implemented in menu-flow-dynamo
- ✅ customer_feedback table created in ROS database
- ✅ Main SME Analytica app has robust sentiment analysis engine
- ❌ **NO CONNECTION** between restaurant feedback and main sentiment analysis

**Integration Benefits:**
1. **Enhanced AI Training**: Real customer data improves sentiment analysis accuracy
2. **Comprehensive Business Intelligence**: Restaurant sentiment data feeds into business analytics
3. **Better Customer Insights**: Combine restaurant feedback with external review data
4. **AI Model Improvement**: Restaurant-specific sentiment data for better predictions

**Technical Implementation Status:**
1. ✅ Created API endpoint to sync customer_feedback to main SME Analytica database
2. ✅ Created restaurant_feedback table in main SME Analytica database
3. ✅ Created restaurant_sentiment_summary table for aggregated analytics
4. ✅ Created FeedbackSyncService for automatic data synchronization  
5. ✅ Added retry logic and sync queue for reliability
6. ❌ Integrate restaurant feedback data into sentiment analysis engine data sources
7. ❌ Update sentiment analysis algorithms to include restaurant feedback scores
8. ✅ Connect frontend feedback forms to use new sync service
9. ✅ Test end-to-end integration and verify data flow

**Data Flow:**
```
Customer Order → Feedback Form → customer_feedback table → SME Analytica Sentiment Engine → Business Intelligence Dashboard
```

**Files to Modify:**
- `app/core/engines/sentiment/engine.py` - Add restaurant feedback data source
- `app/services/analysis.py` - Include restaurant feedback in sentiment calculations
- `frontend/menu-flow-dynamo/src/services/feedbackService.ts` - Add SME Analytica sync
- Database schemas - Ensure proper data sharing permissions

---

## Other Issues
*Additional TODO items will be added as they are identified* 