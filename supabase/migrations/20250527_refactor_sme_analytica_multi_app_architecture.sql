-- SME Analytica Multi-App Architecture Refactoring
-- This migration creates the unified architecture to support multiple applications:
-- 1. SME Analytica Mobile App (business analytics)
-- 2. Restaurant Operating System (ROS) 
-- 3. Connecto (AI voice assistant)

-- =============================================================================
-- CORE ENTITIES (Shared across all apps)
-- =============================================================================

-- Business Types lookup table
CREATE TABLE IF NOT EXISTS public.business_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Unified businesses table (replaces restaurants for all business types)
CREATE TABLE IF NOT EXISTS public.businesses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    address TEXT,
    email TEXT,
    phone TEXT,
    website TEXT,
    logo_url TEXT,
    business_type_id UUID NOT NULL REFERENCES public.business_types(id),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- App modules registry
CREATE TABLE IF NOT EXISTS public.app_modules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    version TEXT DEFAULT '1.0.0',
    icon TEXT,
    category TEXT NOT NULL,
    is_core BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Business-level app module configuration
CREATE TABLE IF NOT EXISTS public.business_app_modules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
    app_module_id UUID NOT NULL REFERENCES public.app_modules(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE,
    settings JSONB DEFAULT '{}',
    activated_at TIMESTAMPTZ DEFAULT NOW(),
    activated_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(business_id, app_module_id)
);

-- User-level app access permissions
CREATE TABLE IF NOT EXISTS public.user_app_access (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    app_module_id UUID NOT NULL REFERENCES public.app_modules(id) ON DELETE CASCADE,
    permissions JSONB DEFAULT '{"read": true, "write": false}',
    granted_at TIMESTAMPTZ DEFAULT NOW(),
    granted_by UUID REFERENCES auth.users(id),
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, app_module_id)
);

-- =============================================================================
-- RESTAURANT-SPECIFIC EXTENSIONS
-- =============================================================================

-- Restaurant-specific details (extends businesses table)
CREATE TABLE IF NOT EXISTS public.restaurant_details (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE UNIQUE,
    cuisine_type TEXT,
    opening_hours JSONB,
    seating_capacity INTEGER,
    delivery_available BOOLEAN DEFAULT FALSE,
    takeout_available BOOLEAN DEFAULT TRUE,
    reservation_required BOOLEAN DEFAULT FALSE,
    dynamic_pricing_enabled BOOLEAN DEFAULT FALSE,
    pricing_rules JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================================================
-- BACKWARD COMPATIBILITY VIEW
-- =============================================================================

-- Unified view that maintains compatibility with existing restaurant queries
CREATE OR REPLACE VIEW public.restaurants_unified AS
SELECT 
    b.id,
    b.user_id,
    b.name,
    b.description,
    b.email as contact_email,
    b.phone as contact_phone,
    b.address,
    b.logo_url,
    COALESCE(rd.opening_hours, '{}') as opening_hours,
    COALESCE(rd.dynamic_pricing_enabled, false) as dynamic_pricing_enabled,
    COALESCE(rd.pricing_rules, '{}') as pricing_rules,
    b.is_active,
    b.created_at,
    b.updated_at
FROM businesses b
LEFT JOIN restaurant_details rd ON rd.business_id = b.id
JOIN business_types bt ON bt.id = b.business_type_id
WHERE bt.name = 'restaurant';

-- =============================================================================
-- INITIAL DATA SETUP
-- =============================================================================

-- Insert default business types
INSERT INTO public.business_types (name, display_name, description) VALUES
    ('restaurant', 'Restaurant', 'Food service establishments'),
    ('retail', 'Retail Store', 'Retail and commerce businesses'),
    ('service', 'Service Business', 'Professional service providers'),
    ('healthcare', 'Healthcare', 'Medical and healthcare services'),
    ('technology', 'Technology', 'Software and technology companies'),
    ('consulting', 'Consulting', 'Business consulting and advisory services')
ON CONFLICT (name) DO NOTHING;

-- Insert core app modules
INSERT INTO public.app_modules (name, display_name, description, category, is_core) VALUES
    ('user_management', 'User Management', 'User profiles, authentication, and access control', 'core', true),
    ('subscription_management', 'Subscription Management', 'Subscription tiers, billing, and plan management', 'core', true),
    ('notifications', 'Notification System', 'Unified notification management across all apps', 'core', true),
    ('restaurant_management', 'Restaurant Management', 'POS, ordering, menu management, and restaurant operations', 'operations', true),
    ('business_analytics', 'Business Analytics', 'Business intelligence, analytics, and reporting', 'analytics', true),
    ('connecto_voice', 'Connecto Voice Assistant', 'AI-powered voice assistant for customer service', 'ai_assistant', true)
ON CONFLICT (name) DO NOTHING;

-- =============================================================================
-- FUNCTIONS AND TRIGGERS
-- =============================================================================

-- Function to automatically set updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_business_types_updated_at BEFORE UPDATE ON business_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_businesses_updated_at BEFORE UPDATE ON businesses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_app_modules_updated_at BEFORE UPDATE ON app_modules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_business_app_modules_updated_at BEFORE UPDATE ON business_app_modules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_app_access_updated_at BEFORE UPDATE ON user_app_access FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_restaurant_details_updated_at BEFORE UPDATE ON restaurant_details FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

CREATE INDEX IF NOT EXISTS idx_businesses_user_id ON businesses(user_id);
CREATE INDEX IF NOT EXISTS idx_businesses_business_type_id ON businesses(business_type_id);
CREATE INDEX IF NOT EXISTS idx_business_app_modules_business_id ON business_app_modules(business_id);
CREATE INDEX IF NOT EXISTS idx_business_app_modules_app_module_id ON business_app_modules(app_module_id);
CREATE INDEX IF NOT EXISTS idx_user_app_access_user_id ON user_app_access(user_id);
CREATE INDEX IF NOT EXISTS idx_user_app_access_app_module_id ON user_app_access(app_module_id);
CREATE INDEX IF NOT EXISTS idx_restaurant_details_business_id ON restaurant_details(business_id);

-- =============================================================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE public.business_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.app_modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_app_modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_app_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.restaurant_details ENABLE ROW LEVEL SECURITY;

-- Business types - public read access
CREATE POLICY "Public read access to business types" ON business_types FOR SELECT TO authenticated, anon USING (true);

-- App modules - public read access for active modules
CREATE POLICY "Public read access to active app modules" ON app_modules FOR SELECT TO authenticated, anon USING (is_active = true);

-- Businesses - users can only access their own businesses
CREATE POLICY "Users can view own businesses" ON businesses FOR SELECT TO authenticated USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own businesses" ON businesses FOR INSERT TO authenticated WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own businesses" ON businesses FOR UPDATE TO authenticated USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own businesses" ON businesses FOR DELETE TO authenticated USING (auth.uid() = user_id);

-- Business app modules - users can only access modules for their businesses
CREATE POLICY "Users can view business app modules for own businesses" ON business_app_modules FOR SELECT TO authenticated 
    USING (EXISTS (SELECT 1 FROM businesses WHERE id = business_id AND user_id = auth.uid()));
CREATE POLICY "Users can manage business app modules for own businesses" ON business_app_modules FOR ALL TO authenticated 
    USING (EXISTS (SELECT 1 FROM businesses WHERE id = business_id AND user_id = auth.uid()));

-- User app access - users can only access their own permissions
CREATE POLICY "Users can view own app access" ON user_app_access FOR SELECT TO authenticated USING (auth.uid() = user_id);

-- Restaurant details - users can only access details for their restaurants
CREATE POLICY "Users can view restaurant details for own businesses" ON restaurant_details FOR SELECT TO authenticated 
    USING (EXISTS (SELECT 1 FROM businesses WHERE id = business_id AND user_id = auth.uid()));
CREATE POLICY "Users can manage restaurant details for own businesses" ON restaurant_details FOR ALL TO authenticated 
    USING (EXISTS (SELECT 1 FROM businesses WHERE id = business_id AND user_id = auth.uid()));

-- =============================================================================
-- MIGRATION COMPLETE
-- =============================================================================

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE 'SME Analytica Multi-App Architecture migration completed successfully';
    RAISE NOTICE 'Created tables: business_types, businesses, app_modules, business_app_modules, user_app_access, restaurant_details';
    RAISE NOTICE 'Created view: restaurants_unified (for backward compatibility)';
    RAISE NOTICE 'Inserted default business types and app modules';
    RAISE NOTICE 'Applied RLS policies and performance indexes';
END $$;
