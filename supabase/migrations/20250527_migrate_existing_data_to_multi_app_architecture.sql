-- Data Migration for SME Analytica Multi-App Architecture
-- This migration populates the new architecture with existing data

-- =============================================================================
-- MIGRATE EXISTING RESTAURANT DATA
-- =============================================================================

-- Migrate existing restaurants to businesses table (if old restaurants table exists)
DO $$
BEGIN
    -- Check if old restaurants table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'restaurants' AND table_schema = 'public') THEN
        RAISE NOTICE 'Migrating data from old restaurants table...';
        
        -- Get restaurant business type ID
        INSERT INTO businesses (
            id,
            name,
            description,
            address,
            email,
            phone,
            logo_url,
            business_type_id,
            user_id,
            is_active,
            created_at,
            updated_at
        )
        SELECT 
            r.id,
            r.name,
            r.description,
            r.address,
            r.contact_email,
            r.contact_phone,
            r.logo_url,
            (SELECT id FROM business_types WHERE name = 'restaurant'),
            r.user_id,
            COALESCE(r.is_active, true),
            COALESCE(r.created_at, NOW()),
            COALESCE(r.updated_at, NOW())
        FROM restaurants r
        ON CONFLICT (id) DO UPDATE SET
            name = EXCLUDED.name,
            description = EXCLUDED.description,
            address = EXCLUDED.address,
            email = EXCLUDED.email,
            phone = EXCLUDED.phone,
            logo_url = EXCLUDED.logo_url,
            updated_at = NOW();
            
        -- Migrate restaurant-specific details
        INSERT INTO restaurant_details (
            business_id,
            opening_hours,
            dynamic_pricing_enabled,
            pricing_rules,
            created_at,
            updated_at
        )
        SELECT 
            r.id,
            COALESCE(r.opening_hours, '{}'),
            COALESCE(r.dynamic_pricing_enabled, false),
            COALESCE(r.pricing_rules, '{}'),
            COALESCE(r.created_at, NOW()),
            COALESCE(r.updated_at, NOW())
        FROM restaurants r
        ON CONFLICT (business_id) DO UPDATE SET
            opening_hours = EXCLUDED.opening_hours,
            dynamic_pricing_enabled = EXCLUDED.dynamic_pricing_enabled,
            pricing_rules = EXCLUDED.pricing_rules,
            updated_at = NOW();
            
        RAISE NOTICE 'Restaurant data migration completed';
    ELSE
        RAISE NOTICE 'No old restaurants table found - skipping restaurant data migration';
    END IF;
END $$;

-- =============================================================================
-- SET UP DEFAULT APP MODULE ACCESS
-- =============================================================================

-- Give all existing users access to core app modules
DO $$
DECLARE
    user_record RECORD;
    module_record RECORD;
BEGIN
    RAISE NOTICE 'Setting up default app module access for existing users...';
    
    -- Loop through all users
    FOR user_record IN SELECT id FROM auth.users LOOP
        -- Give access to all core modules
        FOR module_record IN SELECT id, name FROM app_modules WHERE is_core = true LOOP
            INSERT INTO user_app_access (
                user_id,
                app_module_id,
                permissions,
                granted_at,
                is_active
            ) VALUES (
                user_record.id,
                module_record.id,
                CASE 
                    WHEN module_record.name = 'user_management' THEN '{"read": true, "write": true}'
                    WHEN module_record.name = 'subscription_management' THEN '{"read": true, "write": false}'
                    WHEN module_record.name = 'notifications' THEN '{"read": true, "write": true}'
                    ELSE '{"read": true, "write": true}'
                END,
                NOW(),
                true
            ) ON CONFLICT (user_id, app_module_id) DO NOTHING;
        END LOOP;
    END LOOP;
    
    RAISE NOTICE 'Default app module access setup completed';
END $$;

-- =============================================================================
-- ACTIVATE APP MODULES FOR BUSINESSES
-- =============================================================================

-- Activate restaurant management module for all restaurant businesses
DO $$
DECLARE
    business_record RECORD;
    restaurant_mgmt_module_id UUID;
BEGIN
    RAISE NOTICE 'Activating restaurant management module for restaurant businesses...';
    
    -- Get restaurant management module ID
    SELECT id INTO restaurant_mgmt_module_id FROM app_modules WHERE name = 'restaurant_management';
    
    IF restaurant_mgmt_module_id IS NOT NULL THEN
        -- Activate for all restaurant businesses
        FOR business_record IN 
            SELECT b.id, b.user_id 
            FROM businesses b 
            JOIN business_types bt ON bt.id = b.business_type_id 
            WHERE bt.name = 'restaurant'
        LOOP
            INSERT INTO business_app_modules (
                business_id,
                app_module_id,
                is_active,
                activated_at,
                activated_by
            ) VALUES (
                business_record.id,
                restaurant_mgmt_module_id,
                true,
                NOW(),
                business_record.user_id
            ) ON CONFLICT (business_id, app_module_id) DO UPDATE SET
                is_active = true,
                updated_at = NOW();
        END LOOP;
        
        RAISE NOTICE 'Restaurant management module activation completed';
    ELSE
        RAISE WARNING 'Restaurant management module not found';
    END IF;
END $$;

-- Activate business analytics module for all businesses
DO $$
DECLARE
    business_record RECORD;
    analytics_module_id UUID;
BEGIN
    RAISE NOTICE 'Activating business analytics module for all businesses...';
    
    -- Get business analytics module ID
    SELECT id INTO analytics_module_id FROM app_modules WHERE name = 'business_analytics';
    
    IF analytics_module_id IS NOT NULL THEN
        -- Activate for all businesses
        FOR business_record IN SELECT id, user_id FROM businesses LOOP
            INSERT INTO business_app_modules (
                business_id,
                app_module_id,
                is_active,
                activated_at,
                activated_by
            ) VALUES (
                business_record.id,
                analytics_module_id,
                true,
                NOW(),
                business_record.user_id
            ) ON CONFLICT (business_id, app_module_id) DO UPDATE SET
                is_active = true,
                updated_at = NOW();
        END LOOP;
        
        RAISE NOTICE 'Business analytics module activation completed';
    ELSE
        RAISE WARNING 'Business analytics module not found';
    END IF;
END $$;

-- =============================================================================
-- UPDATE EXISTING FUNCTIONS
-- =============================================================================

-- Update the dynamic pricing function to use the new architecture
CREATE OR REPLACE FUNCTION get_dynamic_pricing_settings(restaurant_id_param UUID)
RETURNS TABLE(
  dynamic_pricing_enabled BOOLEAN,
  pricing_rules JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    r.dynamic_pricing_enabled,
    r.pricing_rules
  FROM restaurants_unified r
  WHERE r.id = restaurant_id_param;
END;
$$;

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Log migration statistics
DO $$
DECLARE
    business_count INTEGER;
    restaurant_count INTEGER;
    app_module_count INTEGER;
    business_app_module_count INTEGER;
    user_app_access_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO business_count FROM businesses;
    SELECT COUNT(*) INTO restaurant_count FROM restaurants_unified;
    SELECT COUNT(*) INTO app_module_count FROM app_modules;
    SELECT COUNT(*) INTO business_app_module_count FROM business_app_modules;
    SELECT COUNT(*) INTO user_app_access_count FROM user_app_access;
    
    RAISE NOTICE '=== MIGRATION STATISTICS ===';
    RAISE NOTICE 'Total businesses: %', business_count;
    RAISE NOTICE 'Total restaurants (via unified view): %', restaurant_count;
    RAISE NOTICE 'Total app modules: %', app_module_count;
    RAISE NOTICE 'Total business-app module activations: %', business_app_module_count;
    RAISE NOTICE 'Total user app access records: %', user_app_access_count;
    RAISE NOTICE '=== MIGRATION COMPLETED SUCCESSFULLY ===';
END $$;
