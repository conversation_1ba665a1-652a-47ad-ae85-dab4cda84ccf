@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 199 100% 49.8%; /* SME Analytica accent blue #11a5e8 */
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 199 100% 49.8%; /* SME Analytica accent blue #11a5e8 */
    --radius: 0.5rem;

    /* SME Analytica brand colors */
    --sme-primary: 215 35% 31%; /* #5f7790 - buttons */
    --sme-accent: 199 100% 49.8%; /* #11a5e8 - links/highlights */
    --sme-background: 215 35% 15%; /* #171f31 - dark background */
    --sme-text: 210 20% 85%; /* #d5dce2 - light text */
  }

  .dark {
    --background: 215 35% 15%; /* SME Analytica dark background #171f31 */
    --foreground: 210 20% 85%; /* SME Analytica light text #d5dce2 */
    --card: 215 35% 18%; /* Slightly lighter than background */
    --card-foreground: 210 20% 85%;
    --popover: 215 35% 15%;
    --popover-foreground: 210 20% 85%;
    --primary: 199 100% 49.8%; /* SME Analytica accent blue #11a5e8 */
    --primary-foreground: 215 35% 15%;
    --secondary: 215 35% 31%; /* SME Analytica button color #5f7790 */
    --secondary-foreground: 210 20% 85%;
    --muted: 215 35% 25%;
    --muted-foreground: 210 15% 65%;
    --accent: 215 35% 25%;
    --accent-foreground: 210 20% 85%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 35% 25%;
    --input: 215 35% 25%;
    --ring: 199 100% 49.8%; /* SME Analytica accent blue #11a5e8 */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .prose-custom h1 {
    @apply text-3xl font-bold tracking-tight lg:text-4xl;
  }

  .prose-custom h2 {
    @apply text-2xl font-semibold tracking-tight lg:text-3xl;
  }

  .prose-custom h3 {
    @apply text-xl font-semibold tracking-tight lg:text-2xl;
  }

  .prose-custom p {
    @apply leading-7 text-muted-foreground;
  }

  .prose-custom code {
    @apply relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold;
  }

  .prose-custom pre {
    @apply overflow-x-auto rounded-lg bg-muted p-4;
  }

  .prose-custom pre code {
    @apply bg-transparent p-0;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles */
.focus-visible {
  @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
}
