import { <PERSON>ada<PERSON> } from "next";
import { <PERSON> } from "@/i18n/routing";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Building,
  TrendingUp,
  Users,
  DollarSign,
  BarChart3,
  Smartphone,
  Globe,
  CheckCircle,
  Star,
  Target,
} from "lucide-react";

export const metadata: Metadata = {
  title: "For Businesses",
  description: "Discover how SME Analytica helps businesses across industries make data-driven decisions and increase revenue through AI-powered insights.",
};

export default function ForBusinessesPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <Badge variant="outline" className="mb-4">
              For Businesses
            </Badge>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              Transform Your Business with AI
            </h1>
            <p className="mb-8 text-xl text-muted-foreground sm:text-2xl">
              SME Analytica's proprietary AI orchestration engine combines multiple state-of-the-art models through ensemble learning 
              to deliver business intelligence previously available only to Fortune 500 companies. Projected ROI potential based on 
              dynamic pricing research across 50+ data sources. Estimated implementation time: under 2 weeks.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/modules">
                  Explore Solutions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="mailto:<EMAIL>">
                  Schedule Demo
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Industry Solutions */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Industry Solutions
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Vertical-specific AI modules designed with projected performance metrics based on industry research
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <Building className="h-8 w-8 text-primary" />
                <CardTitle>Restaurants & Food Service</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Potential for up to 25% revenue increase through AI-driven dynamic pricing, real-time occupancy monitoring, and intelligent table optimization algorithms
                </p>
                <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                  Based on proprietary dynamic pricing research | Designed to maintain 4.0+ star ratings | Optimized for 15-20% faster table turnover with sub-60-second data updates
                </div>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>AI-driven dynamic menu pricing</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>QR-based contactless ordering</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Real-time traffic analytics</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>POS system integration</span>
                  </li>
                </ul>
                <Button asChild className="w-full">
                  <Link href="/modules/menuflow">Learn More</Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Users className="h-8 w-8 text-primary" />
                <CardTitle>Retail & E-commerce</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  AI-powered inventory optimization with automated competitor price monitoring across 50+ data sources for enhanced profit margins and reduced waste
                </p>
                <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                  Potential for 20-30% inventory waste reduction | Designed to increase profit margins by 10-15% | Real-time competitor tracking with advanced pattern recognition
                </div>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Automated competitor price monitoring</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Demand forecasting algorithms</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Inventory optimization analytics</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Customer behavior insights</span>
                  </li>
                </ul>
                <Button asChild className="w-full">
                  <Link href="/modules/sme-app">Learn More</Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Globe className="h-8 w-8 text-primary" />
                <CardTitle>Service Businesses</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  AI-powered patient flow optimization with natural language processing for sentiment analysis and voice AI-assisted appointment management
                </p>
                <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                  Potential for 25-40% wait time reduction | Designed for 95%+ patient satisfaction scores | 24/7 multilingual appointment scheduling with advanced sentiment analysis
                </div>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Voice AI appointment booking</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Patient sentiment analysis</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Resource planning optimization</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Multi-language support</span>
                  </li>
                </ul>
                <Button asChild className="w-full">
                  <Link href="/modules/connecto">Learn More</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Industry Deep Dive */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              How SME Analytica Works Across Industries
            </h2>
            <div className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-3">
                    <Building className="h-6 w-6" />
                    <span>Restaurant & Hospitality</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">Core Challenges</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Optimizing prices during peak vs. off-peak hours</li>
                        <li>• Managing table turnover and capacity</li>
                        <li>• Understanding customer sentiment from reviews</li>
                        <li>• Competing with nearby restaurants</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">SME Analytica Solutions</h4>
                      <ul className="text-sm space-y-1">
                        <li className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>Real-time dynamic pricing based on occupancy</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>QR-based contactless menu management</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>Automated sentiment analysis from Google/Yelp</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>Competitor price monitoring and benchmarking</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-3">
                    <Users className="h-6 w-6" />
                    <span>Retail & E-commerce</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">Core Challenges</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Tracking competitor pricing across platforms</li>
                        <li>• Forecasting demand and managing inventory</li>
                        <li>• Understanding customer behavior patterns</li>
                        <li>• Optimizing profit margins vs. competitiveness</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">SME Analytica Solutions</h4>
                      <ul className="text-sm space-y-1">
                        <li className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>Automated competitor price tracking</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>AI-powered demand forecasting</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>Customer journey and behavior analytics</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>Dynamic pricing optimization algorithms</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-3">
                    <Globe className="h-6 w-6" />
                    <span>Healthcare & Professional Services</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">Core Challenges</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Managing appointment scheduling efficiently</li>
                        <li>• Handling patient/client communications 24/7</li>
                        <li>• Understanding patient satisfaction and feedback</li>
                        <li>• Optimizing resource allocation and staffing</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">SME Analytica Solutions</h4>
                      <ul className="text-sm space-y-1">
                        <li className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>AI voice assistant for appointment booking</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>24/7 multilingual customer support</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>Patient sentiment and satisfaction tracking</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>Resource planning and optimization analytics</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Technical Differentiation */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Beyond Traditional AI Wrappers
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              SME Analytica's proprietary technology stack differentiates it from simple AI wrapper solutions
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2">
            <Card>
              <CardHeader>
                <Target className="h-8 w-8 text-primary" />
                <CardTitle>Proprietary AI Orchestration Engine</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Multi-Model Ensemble Learning</h4>
                  <p className="text-sm text-muted-foreground">
                    Combines multiple state-of-the-art AI models through proprietary confidence weighting and dynamic model selection algorithms
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Intelligent Model Selection</h4>
                  <p className="text-sm text-muted-foreground">
                    Analysis-specific model routing based on task complexity, data type, and industry context with real-time performance monitoring
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Confidence Scoring System</h4>
                  <p className="text-sm text-muted-foreground">
                    Multi-factor confidence calculation combining data quality, model confidence, freshness, and coverage metrics for reliable insights
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <BarChart3 className="h-8 w-8 text-primary" />
                <CardTitle>Industry-Specific Business Intelligence</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Vertical-Specific Training</h4>
                  <p className="text-sm text-muted-foreground">
                    Models fine-tuned for restaurant, retail, healthcare, and hospitality industries with specialized prompting and business context
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Real-Time Data Pipeline</h4>
                  <p className="text-sm text-muted-foreground">
                    Automated ingestion from Google Places, TripAdvisor, Yelp, and POS systems with intelligent data quality assessment and validation
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Advanced Pricing Algorithms</h4>
                  <p className="text-sm text-muted-foreground">
                    Proprietary dynamic pricing engine analyzing 15+ variables including weather, events, competitor activity, and demand patterns
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Why Choose SME Analytica?
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Projected business impact based on dynamic pricing research and AI optimization studies
            </p>
          </div>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {[
              {
                icon: TrendingUp,
                title: "Increase Revenue",
                description: "Projected revenue increase potential through proprietary AI orchestration and ensemble learning",
                metric: "25%",
              },
              {
                icon: Users,
                title: "Better Decisions",
                description: "Multi-model AI intelligence with confidence scoring replaces guesswork",
                metric: "100%",
              },
              {
                icon: DollarSign,
                title: "Cost Effective",
                description: "Fortune 500-level AI capabilities at SME-accessible pricing with white-label options",
                metric: "90%",
              },
              {
                icon: BarChart3,
                title: "Real-time Insights",
                description: "Real-time processing from 50+ data sources with sub-60-second market intelligence updates",
                metric: "24/7",
              },
            ].map((benefit, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <benefit.icon className="mx-auto h-12 w-12 text-primary" />
                  <div className="text-3xl font-bold text-primary">{benefit.metric}</div>
                  <CardTitle className="text-lg">{benefit.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{benefit.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              Projected Case Studies
            </h2>
            <div className="grid gap-8 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <div className="flex items-center space-x-2">
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                  </div>
                  <CardTitle>Restaurant Optimization Scenario</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    Based on proprietary multi-model AI research optimized for pricing and market analysis, 
                    a typical restaurant implementing MenuFlow could potentially achieve 20-25% revenue increases during peak hours 
                    while maintaining 4.0+ star ratings through intelligent QR-based ordering and real-time occupancy monitoring.
                  </p>
                  <div className="flex items-center space-x-4 text-sm">
                    <Badge variant="secondary">Projected +20-25% Revenue</Badge>
                    <Badge variant="secondary">15-20% Faster Table Turnover</Badge>
                    <Badge variant="secondary">4.0+ Star Rating Maintenance</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="flex items-center space-x-2">
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                    <Star className="h-5 w-5 text-yellow-500" />
                  </div>
                  <CardTitle>Retail Intelligence Scenario</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    SME App's AI orchestration engine leverages advanced algorithms for trend analysis and sentiment processing 
                    to deliver competitive intelligence across 50+ data sources. The platform is designed to help retail businesses 
                    identify market opportunities and optimize pricing strategies with 15+ environmental variables and real-time competitor tracking.
                  </p>
                  <div className="flex items-center space-x-4 text-sm">
                    <Badge variant="secondary">50+ Data Sources</Badge>
                    <Badge variant="secondary">Sub-60s Market Updates</Badge>
                    <Badge variant="secondary">Multi-Model AI Analysis</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Getting Started */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Get Started Today
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Choose the solution that fits your business needs
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-3">
            {[
              {
                title: "MenuFlow",
                description: "AI-driven restaurant optimization with proprietary dynamic pricing algorithms",
                price: "Contact for pricing",
                features: ["Real-time dynamic pricing", "QR-based contactless ordering", "Traffic analytics dashboard", "Multi-POS integration (Toast, Square, Clover)"],
                cta: "Try MenuFlow",
                href: "https://restaurants.smeanalytica.dev",
                external: true,
              },
              {
                title: "SME App",
                description: "Cross-platform BI with multi-model AI orchestration engine",
                price: "Free trial available",
                features: ["50+ data sources analysis", "Real-time competitor tracking", "Predictive sales forecasting", "Native iOS/Android apps"],
                cta: "Download App",
                href: "https://testflight.apple.com/join/kCjhqR4Q",
                external: true,
              },
              {
                title: "Enterprise AI",
                description: "White-label AI orchestration with custom model fine-tuning",
                price: "Custom pricing",
                features: ["Industry-specific model training", "Dedicated cloud infrastructure", "99.9% uptime SLA", "Custom API endpoints"],
                cta: "Contact Sales",
                href: "mailto:<EMAIL>",
                external: true,
              },
            ].map((plan, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <CardTitle className="text-xl">{plan.title}</CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                  <div className="text-2xl font-bold text-primary">{plan.price}</div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2 text-sm">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button asChild className="w-full">
                    {plan.external ? (
                      <a href={plan.href} target="_blank" rel="noopener noreferrer">
                        {plan.cta}
                      </a>
                    ) : (
                      <Link href={plan.href}>{plan.cta}</Link>
                    )}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to Deploy Enterprise-Grade AI?
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Join businesses leveraging SME Analytica's proprietary AI orchestration engine to compete with Fortune 500 companies. 
              Implementation in under 2 weeks with potential 25% revenue increases through intelligent automation.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/modules">
                  Explore Solutions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="mailto:<EMAIL>">
                  Contact Sales
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
