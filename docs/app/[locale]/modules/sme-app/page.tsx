import { <PERSON>ada<PERSON> } from "next";
import { <PERSON> } from "@/i18n/routing";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Smartphone,
  TrendingUp,
  BarChart3,
  Users,
  Globe,
  Zap,
  CheckCircle,
  Download,
  Star,
  Eye,
  Brain,
  Shield,
  Languages,
} from "lucide-react";

export const metadata: Metadata = {
  title: "SME App - Mobile Business Analytics",
  description: "SME App is SME Analytica's mobile business intelligence platform with AI-powered analytics, sentiment analysis, and competitor benchmarking.",
};

export default function SMEAppPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="p-2 rounded-lg bg-blue-500 text-white">
                <Smartphone className="h-6 w-6" />
              </div>
              <Badge variant="default">Live on TestFlight</Badge>
            </div>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              SME App
            </h1>
            <p className="mb-8 text-xl text-muted-foreground sm:text-2xl">
              AI-powered mobile business intelligence platform for small and medium enterprises. 
              Get actionable insights, competitor analysis, and market intelligence on the go.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="https://testflight.apple.com/join/kCjhqR4Q" target="_blank">
                  <Download className="mr-2 h-4 w-4" />
                  Download on TestFlight
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="#features">
                  Explore Features
                </Link>
              </Button>
              <Button variant="ghost" size="lg" asChild>
                <Link href="/integrations#sme-app">
                  API Integration
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Overview */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl">
              What is SME App?
            </h2>
            <div className="mb-8 space-y-4 text-lg text-muted-foreground">
              <p>
                SME App is the mobile companion to SME Analytica's business intelligence platform, 
                built with React Native and Expo for cross-platform compatibility. Currently in 
                pre-launch testing on TestFlight, the app provides business owners with powerful 
                AI-driven insights directly on their mobile devices.
              </p>
              <p>
                The app features comprehensive business analysis tools including market analysis, 
                competitor benchmarking, sentiment tracking, and sales forecasting. With support 
                for multiple languages and real-time data synchronization, SME App makes enterprise-level 
                business intelligence accessible to small and medium businesses.
              </p>
            </div>
            <div className="grid gap-6 md:grid-cols-3">
              <Card className="text-center">
                <CardHeader>
                  <Brain className="mx-auto h-12 w-12 text-blue-500" />
                  <CardTitle>AI-Powered Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Advanced AI models provide actionable business insights and recommendations
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardHeader>
                  <TrendingUp className="mx-auto h-12 w-12 text-blue-500" />
                  <CardTitle>Real-time Insights</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Live market data and competitor analysis updated in real-time
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardHeader>
                  <Smartphone className="mx-auto h-12 w-12 text-blue-500" />
                  <CardTitle>Mobile-First Design</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Native mobile experience optimized for business owners on the go
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section id="features" className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Core Features
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Everything you need to make data-driven business decisions
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2">
            <Card>
              <CardHeader>
                <BarChart3 className="h-8 w-8 text-blue-500" />
                <CardTitle>Business Analytics Dashboard</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Multiple Analysis Types</h4>
                    <p className="text-sm text-muted-foreground">Market, competitor, pricing, sentiment, growth, and sales forecasting</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Analysis History</h4>
                    <p className="text-sm text-muted-foreground">Track and compare past analyses with insights button</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">AI-Generated Insights</h4>
                    <p className="text-sm text-muted-foreground">Actionable recommendations powered by advanced AI models</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Users className="h-8 w-8 text-blue-500" />
                <CardTitle>User Management & Profiles</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Profile & Banner Images</h4>
                    <p className="text-sm text-muted-foreground">Upload and manage profile pictures with Cloudinary integration</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Subscription Management</h4>
                    <p className="text-sm text-muted-foreground">Premium subscriptions with 50 analyses per month</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Secure Authentication</h4>
                    <p className="text-sm text-muted-foreground">Supabase-powered authentication with JWT tokens</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Languages className="h-8 w-8 text-blue-500" />
                <CardTitle>Internationalization</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Multi-language Support</h4>
                    <p className="text-sm text-muted-foreground">English, Spanish, and French with i18next integration</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Dynamic Translation</h4>
                    <p className="text-sm text-muted-foreground">Real-time language switching with GeneralTranslation.com</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Localized Analysis</h4>
                    <p className="text-sm text-muted-foreground">Analysis results translated to user's preferred language</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Shield className="h-8 w-8 text-blue-500" />
                <CardTitle>Enterprise Features</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Dark/Light Theme</h4>
                    <p className="text-sm text-muted-foreground">Adaptive themes for comfortable viewing in any environment</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Offline Capability</h4>
                    <p className="text-sm text-muted-foreground">Fallback to local translations when offline</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Health Monitoring</h4>
                    <p className="text-sm text-muted-foreground">Automatic health checks and error reporting</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Technology Stack */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              Technology Stack
            </h2>
            <div className="grid gap-8 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <Smartphone className="h-8 w-8 text-blue-500" />
                  <CardTitle>Mobile Development</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">React Native + Expo</h4>
                    <p className="text-sm text-muted-foreground">Cross-platform mobile development with native performance</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">TypeScript</h4>
                    <p className="text-sm text-muted-foreground">Type-safe development with enhanced developer experience</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Expo Router</h4>
                    <p className="text-sm text-muted-foreground">File-based routing with authentication guards</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Globe className="h-8 w-8 text-blue-500" />
                  <CardTitle>Backend Integration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">SME Analytica API</h4>
                    <p className="text-sm text-muted-foreground">FastAPI backend at api.smeanalytica.dev</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Supabase</h4>
                    <p className="text-sm text-muted-foreground">Authentication, database, and real-time subscriptions</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Cloudinary</h4>
                    <p className="text-sm text-muted-foreground">Image storage and optimization for profile pictures</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* TestFlight Information */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Join the Beta Testing
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              SME App is currently in pre-launch testing. Join our TestFlight beta to experience 
              the future of mobile business intelligence.
            </p>
            <Card className="p-8">
              <div className="grid gap-8 md:grid-cols-2 items-center">
                <div className="text-left">
                  <h3 className="text-2xl font-bold mb-4">What's Included in Beta</h3>
                  <ul className="space-y-2 text-muted-foreground">
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="h-5 w-5 text-blue-500" />
                      <span>Full access to all analysis types</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="h-5 w-5 text-blue-500" />
                      <span>Premium subscription features</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="h-5 w-5 text-blue-500" />
                      <span>Multi-language support</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <CheckCircle className="h-5 w-5 text-blue-500" />
                      <span>Direct feedback channel to development team</span>
                    </li>
                  </ul>
                </div>
                <div className="text-center">
                  <div className="mb-4">
                    <Star className="mx-auto h-16 w-16 text-yellow-500" />
                  </div>
                  <Button size="lg" asChild>
                    <Link href="https://testflight.apple.com/join/kCjhqR4Q" target="_blank">
                      <Download className="mr-2 h-4 w-4" />
                      Join TestFlight Beta
                    </Link>
                  </Button>
                  <p className="mt-2 text-sm text-muted-foreground">
                    iOS 15+ required
                  </p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to Transform Your Business?
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Join thousands of business owners using SME App to make data-driven decisions
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="https://testflight.apple.com/join/kCjhqR4Q" target="_blank">
                  <Download className="mr-2 h-4 w-4" />
                  Download SME App
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/for-businesses">
                  Learn More
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
