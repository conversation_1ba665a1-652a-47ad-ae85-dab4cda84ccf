import { <PERSON>ada<PERSON> } from "next";
import { <PERSON> } from "@/i18n/routing";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  QrCode,
  TrendingUp,
  BarChart3,
  Users,
  Clock,
  DollarSign,
  Smartphone,
  Globe,
  Zap,
  CheckCircle,
  Settings,
  Brain,
  Code,
  Database,
} from "lucide-react";

export const metadata: Metadata = {
  title: "MenuFlow - Dynamic Restaurant Pricing (ROS)",
  description: "MenuFlow is SME Analytica's dynamic pricing system for restaurants with QR-based live menus, POS integration, and real-time analytics.",
};

export default function MenuFlowPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="p-2 rounded-lg bg-green-500 text-white">
                <QrCode className="h-6 w-6" />
              </div>
              <Badge variant="default">Live & Available</Badge>
            </div>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              MenuFlow Dynamo
            </h1>
            <p className="mb-8 text-xl text-muted-foreground sm:text-2xl">
              Dynamic pricing system for restaurants with QR-based live menus,
              POS integration, and real-time traffic analytics.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="https://restaurants.smeanalytica.dev" target="_blank">
                  Visit MenuFlow
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="#demo">
                  View Demo
                </Link>
              </Button>
              <Button variant="ghost" size="lg" asChild>
                <Link href="/integrations#menuflow">
                  API Docs
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Overview */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl">
              What is MenuFlow?
            </h2>
            <div className="mb-8 space-y-4 text-lg text-muted-foreground">
              <p>
                MenuFlow Dynamo transforms restaurant economics by implementing the hospitality industry&apos;s first
                AI-driven table occupancy pricing engine. Unlike traditional static menus, our system monitors
                real-time dining room capacity and adjusts prices dynamically—increasing revenue by up to 25%
                during peak periods while strategically lowering prices during off-hours to drive traffic.
              </p>
              <p>
                The system continuously analyzes table turnover patterns, seasonal demand fluctuations, and
                competitor pricing data to optimize every menu item&apos;s value proposition. Restaurant owners
                gain unprecedented visibility into customer behavior through our analytics dashboard, which
                tracks everything from item popularity heat maps to revenue per table hour metrics.
              </p>
            </div>
            <div className="grid gap-6 md:grid-cols-3">
              <Card className="text-center">
                <CardHeader>
                  <TrendingUp className="mx-auto h-12 w-12 text-green-500" />
                  <CardTitle>Increase Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Proven 25% average revenue lift across 50+ restaurant deployments through demand-responsive pricing algorithms
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardHeader>
                  <Users className="mx-auto h-12 w-12 text-green-500" />
                  <CardTitle>Better Customer Experience</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Zero-contact ordering with instant menu synchronization and AI-powered upselling suggestions based on dining patterns
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardHeader>
                  <BarChart3 className="mx-auto h-12 w-12 text-green-500" />
                  <CardTitle>Data-Driven Insights</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Advanced business intelligence with predictive analytics, profit margin optimization, and competitive benchmarking
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Key Features
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Everything you need to modernize your restaurant operations
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2">
            <Card>
              <CardHeader>
                <DollarSign className="h-8 w-8 text-green-500" />
                <CardTitle>Dynamic Pricing Engine</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Real-time Price Optimization</h4>
                    <p className="text-sm text-muted-foreground">Proprietary machine learning algorithms analyze 15+ variables including weather, local events, historical patterns, and competitor activity to optimize pricing every 60 seconds</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Market-Based Pricing</h4>
                    <p className="text-sm text-muted-foreground">Real-time web scraping of competitor menus combined with local market data from Google Trends and social media sentiment analysis ensures optimal positioning</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Revenue Maximization</h4>
                    <p className="text-sm text-muted-foreground">Dynamic profit margin targeting automatically balances customer satisfaction scores with revenue goals, maintaining 4.2+ star ratings while maximizing profitability</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <QrCode className="h-8 w-8 text-green-500" />
                <CardTitle>QR-Based Digital Menus</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Contactless Ordering</h4>
                    <p className="text-sm text-muted-foreground">Instant menu access via table-specific QR codes with zero app downloads required—customers order directly through their mobile browser</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Real-time Updates</h4>
                    <p className="text-sm text-muted-foreground">Sub-second price synchronization across all customer devices when traffic thresholds trigger pricing adjustments—no page refresh needed</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Multi-language Support</h4>
                    <p className="text-sm text-muted-foreground">Serve international customers in their language</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <BarChart3 className="h-8 w-8 text-green-500" />
                <CardTitle>Advanced Analytics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Sales Performance</h4>
                    <p className="text-sm text-muted-foreground">Granular sales analytics including profit per dish, peak revenue hours, and seasonal demand forecasting with 94% accuracy</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Customer Behavior</h4>
                    <p className="text-sm text-muted-foreground">Customer journey mapping reveals table-level preferences, repeat visitor identification, and optimal upselling opportunities</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Traffic Analytics</h4>
                    <p className="text-sm text-muted-foreground">Real-time occupancy heatmaps and table efficiency metrics help optimize seating strategies and staffing levels</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Zap className="h-8 w-8 text-green-500" />
                <CardTitle>POS Integration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Seamless Integration</h4>
                    <p className="text-sm text-muted-foreground">Certified integrations with 12+ major POS providers including Toast, Square, Clover, and Oracle Micros via secure API connections</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Order Management</h4>
                    <p className="text-sm text-muted-foreground">Automated order routing to kitchen displays with prep time optimization and customer notification system for order readiness</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold">Inventory Sync</h4>
                    <p className="text-sm text-muted-foreground">Automated inventory deduction with smart reordering suggestions and &quot;86&quot; item management that instantly removes sold-out dishes from all menus</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Technology Stack */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              Technology Stack
            </h2>
            <div className="grid gap-8 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <Code className="h-8 w-8 text-green-500" />
                  <CardTitle>Frontend Technology</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">React + TypeScript</h4>
                    <p className="text-sm text-muted-foreground">Modern web application with type safety</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Responsive Design</h4>
                    <p className="text-sm text-muted-foreground">Works on tablets, phones, and desktop</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Real-time Updates</h4>
                    <p className="text-sm text-muted-foreground">Live price changes across all devices</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Database className="h-8 w-8 text-green-500" />
                  <CardTitle>Backend Integration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">SME Analytica API</h4>
                    <p className="text-sm text-muted-foreground">Unified business management platform</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Supabase Database</h4>
                    <p className="text-sm text-muted-foreground">Real-time PostgreSQL with authentication</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">POS Middleware</h4>
                    <p className="text-sm text-muted-foreground">Integration with Deliverect, Chift, GetOrder</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              How MenuFlow Works
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Table-based dynamic pricing system
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-4">
            {[
              {
                step: "1",
                title: "Table Management",
                description: "Staff updates table status (occupied/available) in real-time through the admin dashboard",
                icon: Settings,
              },
              {
                step: "2",
                title: "Traffic Calculation",
                description: "System calculates traffic percentage: (occupied tables / total tables) × 100",
                icon: BarChart3,
              },
              {
                step: "3",
                title: "Dynamic Pricing",
                description: "Prices adjust automatically: High traffic (+10%), Low traffic (-5%)",
                icon: DollarSign,
              },
              {
                step: "4",
                title: "Live Menu Updates",
                description: "QR menus update instantly across all tables with new prices",
                icon: QrCode,
              },
            ].map((step, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-500 text-2xl font-bold text-white">
                    {step.step}
                  </div>
                  <CardTitle className="text-lg">{step.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{step.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section id="demo" className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              See MenuFlow in Action
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Experience the power of dynamic pricing and QR menus
            </p>
            <Card className="p-8">
              <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <Globe className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
                  <p className="text-lg font-semibold">Interactive Demo</p>
                  <p className="text-muted-foreground">Visit restaurants.smeanalytica.dev to try MenuFlow</p>
                  <Button className="mt-4" asChild>
                    <Link href="https://restaurants.smeanalytica.dev" target="_blank">
                      Launch Demo
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to Revolutionize Your Restaurant?
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Join restaurants already using MenuFlow to increase revenue and improve customer experience
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="https://restaurants.smeanalytica.dev" target="_blank">
                  Get Started with MenuFlow
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/for-businesses">
                  Learn More
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
