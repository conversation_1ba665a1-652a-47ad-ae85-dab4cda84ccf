'use client';

import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/routing';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import Image from 'next/image';
import { 
  Calendar, 
  TrendingUp, 
  Users, 
  BarChart3, 
  Ticket, 
  DollarSign, 
  MapPin, 
  Clock,
  ArrowLeft,
  CheckCircle,
  Circle,
  Music,
  Camera,
  Gamepad2,
  GraduationCap,
  Building,
  Zap
} from 'lucide-react';

export default function TicketingPage() {
  const t = useTranslations('modules.ticketing');
  const tCommon = useTranslations('common');

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        {/* Back Navigation */}
        <Button variant="ghost" asChild className="mb-6">
          <Link href="/modules" className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Modules
          </Link>
        </Button>

        {/* Header Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="relative">
              {/* Animated glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-xl animate-pulse" />
              <div className="relative bg-card border-2 border-purple-500/20 rounded-full p-6">
                <Ticket className="h-16 w-16 text-purple-600" />
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-center gap-2 mb-2">
              <h1 className="text-4xl font-bold text-foreground">{t('title')}</h1>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {t('status')}
              </Badge>
            </div>
            <p className="text-xl text-muted-foreground">{t('subtitle')}</p>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              {t('description')}
            </p>
          </div>
        </div>

        {/* Planning Progress */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-primary" />
              Development Timeline
            </CardTitle>
            <CardDescription>Current planning and development status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Planning Progress</span>
                <span className="text-sm text-muted-foreground">25%</span>
              </div>
              <Progress value={25} className="w-full" />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Market Research</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Feature Specification</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Circle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Technical Architecture</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Circle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">UI/UX Design</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Circle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">AI Model Development</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Circle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Platform Development</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Circle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Beta Testing</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Circle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Public Launch</span>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>Expected Development Start:</strong> Q2 2025<br />
                  <strong>Estimated Launch:</strong> Q4 2025
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Planned Features */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-primary" />
                Planned Features
              </CardTitle>
              <CardDescription>Revolutionary event management capabilities</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                <TrendingUp className="h-5 w-5 text-green-600 mt-1" />
                <div>
                  <h4 className="font-medium">Dynamic Pricing AI</h4>
                  <p className="text-sm text-muted-foreground">AI-powered pricing optimization based on demand, time, and market conditions</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                <BarChart3 className="h-5 w-5 text-blue-600 mt-1" />
                <div>
                  <h4 className="font-medium">Demand Forecasting</h4>
                  <p className="text-sm text-muted-foreground">Predict ticket sales and optimize inventory management</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                <Users className="h-5 w-5 text-purple-600 mt-1" />
                <div>
                  <h4 className="font-medium">Audience Analytics</h4>
                  <p className="text-sm text-muted-foreground">Deep insights into attendee behavior and preferences</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                <DollarSign className="h-5 w-5 text-orange-600 mt-1" />
                <div>
                  <h4 className="font-medium">Revenue Optimization</h4>
                  <p className="text-sm text-muted-foreground">Maximize revenue through intelligent pricing strategies</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Event Types */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-primary" />
                Event Types
              </CardTitle>
              <CardDescription>Designed for diverse event categories</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-2 rounded-md">
                  <Music className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Concerts & Music Events</span>
                </div>
                
                <div className="flex items-center gap-3 p-2 rounded-md">
                  <Building className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Corporate Events</span>
                </div>
                
                <div className="flex items-center gap-3 p-2 rounded-md">
                  <GraduationCap className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Conferences & Seminars</span>
                </div>
                
                <div className="flex items-center gap-3 p-2 rounded-md">
                  <Gamepad2 className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Sports Events</span>
                </div>
                
                <div className="flex items-center gap-3 p-2 rounded-md">
                  <Camera className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Workshops & Classes</span>
                </div>
                
                <div className="flex items-center gap-3 p-2 rounded-md">
                  <MapPin className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Local Community Events</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Platform Capabilities */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              Platform Capabilities
            </CardTitle>
            <CardDescription>Comprehensive event management solution</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="text-center p-4">
                <Ticket className="h-8 w-8 text-blue-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Smart Ticketing</h4>
                <p className="text-sm text-muted-foreground">Multiple ticket types with QR codes and digital validation</p>
              </div>
              
              <div className="text-center p-4">
                <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Dynamic Pricing</h4>
                <p className="text-sm text-muted-foreground">AI-driven price adjustments based on demand and market conditions</p>
              </div>
              
              <div className="text-center p-4">
                <BarChart3 className="h-8 w-8 text-purple-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Real-time Analytics</h4>
                <p className="text-sm text-muted-foreground">Live sales tracking and attendance monitoring</p>
              </div>
              
              <div className="text-center p-4">
                <Users className="h-8 w-8 text-orange-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Audience Insights</h4>
                <p className="text-sm text-muted-foreground">Detailed demographics and behavior analysis</p>
              </div>
              
              <div className="text-center p-4">
                <MapPin className="h-8 w-8 text-red-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Venue Management</h4>
                <p className="text-sm text-muted-foreground">Seating charts, capacity management, and layout optimization</p>
              </div>
              
              <div className="text-center p-4">
                <Clock className="h-8 w-8 text-indigo-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Event Scheduling</h4>
                <p className="text-sm text-muted-foreground">Multi-event calendar with automated reminders</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* AI Advantages */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>AI-Powered Advantages</CardTitle>
            <CardDescription>How AI transforms traditional event ticketing</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-lg">Revenue Optimization</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Potential to increase revenue up to 30% with dynamic pricing</li>
                  <li>• Predict optimal pricing windows</li>
                  <li>• Minimize unsold inventory</li>
                  <li>• Maximize profit margins per event</li>
                </ul>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-semibold text-lg">Smart Forecasting</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Designed to predict attendance with high accuracy</li>
                  <li>• Optimize marketing spend allocation</li>
                  <li>• Plan venue capacity effectively</li>
                  <li>• Reduce event planning risks</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Interest Form */}
        <Card>
          <CardHeader className="text-center">
            <CardTitle>Express Your Interest</CardTitle>
            <CardDescription>
              Be among the first to know when our Event Ticketing Platform enters development. 
              Your feedback will help shape the features and priorities.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="flex flex-wrap justify-center gap-4">
              <Button asChild size="lg">
                <Link href="mailto:<EMAIL>?subject=Event%20Ticketing%20Platform%20Interest">
                  Express Interest
                </Link>
              </Button>
              
              <Button asChild variant="outline" size="lg">
                <Link href="/modules">
                  Explore Available Modules
                </Link>
              </Button>
            </div>
            
            <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-950/20 rounded-lg border border-amber-200 dark:border-amber-800">
              <p className="text-sm text-amber-800 dark:text-amber-200">
                <strong>Early Bird Benefits:</strong> First 100 interested parties will receive 50% off the first year 
                when the platform launches, plus input on feature development.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}