'use client';

import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/routing';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import Image from 'next/image';
import { 
  Bot, 
  Mic, 
  Languages, 
  Clock, 
  Building2, 
  Phone, 
  MessageSquare, 
  Zap,
  ArrowLeft,
  Calendar,
  CheckCircle,
  Circle,
  Users,
  BarChart3,
  Shield,
  Headphones
} from 'lucide-react';

export default function ConnectoPage() {
  const t = useTranslations('modules.connecto');
  const tCommon = useTranslations('common');

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        {/* Back Navigation */}
        <Button variant="ghost" asChild className="mb-6">
          <Link href="/modules" className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Modules
          </Link>
        </Button>

        {/* Header Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="relative">
              {/* Animated glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl animate-pulse" />
              <div className="relative bg-card border-2 border-blue-500/20 rounded-full p-6">
                <Bot className="h-16 w-16 text-blue-600" />
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-center gap-2 mb-2">
              <h1 className="text-4xl font-bold text-foreground">{t('title')}</h1>
              <Badge variant="secondary" className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                {t('status')}
              </Badge>
            </div>
            <p className="text-xl text-muted-foreground">{t('subtitle')}</p>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              {t('description')}
            </p>
          </div>
        </div>

        {/* Development Progress */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-primary" />
              {t('progress.title')}
            </CardTitle>
            <CardDescription>{t('progress.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Overall Progress</span>
                <span className="text-sm text-muted-foreground">{t('progress.overall')}</span>
              </div>
              <Progress value={65} className="w-full" />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">AI Model Training</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Voice Recognition Engine</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Multi-language Support</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Circle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Business Integrations</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Circle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Dashboard Interface</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Circle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Call Analytics</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Circle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Beta Testing</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Circle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Public Release</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Key Features */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-primary" />
                {t('features.title')}
              </CardTitle>
              <CardDescription>What makes Connecto unique</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                <Mic className="h-5 w-5 text-blue-600 mt-1" />
                <div>
                  <h4 className="font-medium">{t('features.voiceAI.title')}</h4>
                  <p className="text-sm text-muted-foreground">{t('features.voiceAI.description')}</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                <Languages className="h-5 w-5 text-green-600 mt-1" />
                <div>
                  <h4 className="font-medium">{t('features.multiLanguage.title')}</h4>
                  <p className="text-sm text-muted-foreground">{t('features.multiLanguage.description')}</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                <Clock className="h-5 w-5 text-purple-600 mt-1" />
                <div>
                  <h4 className="font-medium">{t('features.availability.title')}</h4>
                  <p className="text-sm text-muted-foreground">{t('features.availability.description')}</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                <Building2 className="h-5 w-5 text-orange-600 mt-1" />
                <div>
                  <h4 className="font-medium">{t('features.integration.title')}</h4>
                  <p className="text-sm text-muted-foreground">{t('features.integration.description')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Use Cases */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-primary" />
                Perfect For
              </CardTitle>
              <CardDescription>Industries that benefit most from Connecto</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-2 rounded-md">
                  <Phone className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Medical Practices</span>
                </div>
                
                <div className="flex items-center gap-3 p-2 rounded-md">
                  <Calendar className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Service Businesses</span>
                </div>
                
                <div className="flex items-center gap-3 p-2 rounded-md">
                  <Building2 className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Real Estate Agencies</span>
                </div>
                
                <div className="flex items-center gap-3 p-2 rounded-md">
                  <Headphones className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Customer Support</span>
                </div>
                
                <div className="flex items-center gap-3 p-2 rounded-md">
                  <MessageSquare className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Restaurants</span>
                </div>
                
                <div className="flex items-center gap-3 p-2 rounded-md">
                  <Shield className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Professional Services</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Capabilities Preview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              What Connecto Can Do
            </CardTitle>
            <CardDescription>Comprehensive AI receptionist capabilities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="text-center p-4">
                <Phone className="h-8 w-8 text-blue-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Call Handling</h4>
                <p className="text-sm text-muted-foreground">Answer calls professionally with personalized greetings</p>
              </div>
              
              <div className="text-center p-4">
                <Calendar className="h-8 w-8 text-green-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Appointment Scheduling</h4>
                <p className="text-sm text-muted-foreground">Book, reschedule, and manage appointments automatically</p>
              </div>
              
              <div className="text-center p-4">
                <MessageSquare className="h-8 w-8 text-purple-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Customer Support</h4>
                <p className="text-sm text-muted-foreground">Provide instant answers to common questions</p>
              </div>
              
              <div className="text-center p-4">
                <Building2 className="h-8 w-8 text-orange-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Lead Qualification</h4>
                <p className="text-sm text-muted-foreground">Qualify leads and route to appropriate team members</p>
              </div>
              
              <div className="text-center p-4">
                <BarChart3 className="h-8 w-8 text-red-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Call Analytics</h4>
                <p className="text-sm text-muted-foreground">Track call patterns and customer satisfaction</p>
              </div>
              
              <div className="text-center p-4">
                <Shield className="h-8 w-8 text-indigo-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Secure & Reliable</h4>
                <p className="text-sm text-muted-foreground">Enterprise-grade security with 99.9% uptime</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Get Notified */}
        <Card>
          <CardHeader className="text-center">
            <CardTitle>{t('waitlist.title')}</CardTitle>
            <CardDescription>
              {t('waitlist.description')}
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="flex flex-wrap justify-center gap-4">
              <Button asChild size="lg">
                <Link href="mailto:<EMAIL>?subject=Connecto%20Waitlist">
                  {t('waitlist.joinWaitlist')}
                </Link>
              </Button>
              
              <Button asChild variant="outline" size="lg">
                <Link href="/modules">
                  {t('waitlist.exploreOther')}
                </Link>
              </Button>
            </div>
            
            <p className="text-sm text-muted-foreground mt-4">
              {t('waitlist.expectedLaunch')}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}