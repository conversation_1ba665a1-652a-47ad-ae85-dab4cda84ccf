import { useTranslations } from "next-intl";
import { <PERSON> } from "@/i18n/routing";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  BarChart3,
  Brain,
  Zap,
  Globe,
  Smartphone,
  Code,
  TrendingUp,
  Users,
  Settings,
  Database,
  Cloud,
} from "lucide-react";

export default function HomePage() {
  const t = useTranslations("home");

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <Badge variant="outline" className="mb-4">
              AI Business Intelligence Platform
            </Badge>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              {t("hero.title")}
            </h1>
            <p className="mb-8 text-xl text-muted-foreground sm:text-2xl">
              {t("hero.description")}
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/platform">
                  {t("hero.getStarted")}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/modules">
                  {t("hero.viewDemo")}
                </Link>
              </Button>
              <Button variant="ghost" size="lg" asChild>
                <Link href="https://api.smeanalytica.dev/docs" target="_blank">
                  {t("hero.apiDocs")}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Overview Section */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              {t("overview.title")}
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              {t("overview.description")}
            </p>
          </div>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {[
              {
                icon: Brain,
                title: t("overview.features.0.title"),
                description: t("overview.features.0.description"),
              },
              {
                icon: TrendingUp,
                title: t("overview.features.1.title"),
                description: t("overview.features.1.description"),
              },
              {
                icon: BarChart3,
                title: t("overview.features.2.title"),
                description: t("overview.features.2.description"),
              },
              {
                icon: Globe,
                title: t("overview.features.3.title"),
                description: t("overview.features.3.description"),
              },
            ].map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <feature.icon className="mx-auto h-12 w-12 text-primary" />
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Modules Section */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              {t("modules.title")}
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              {t("modules.description")}
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2">
            {/* MenuFlow */}
            <Card className="group hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-2xl">{t("modules.menuflow.title")}</CardTitle>
                  <Badge>Live</Badge>
                </div>
                <CardDescription className="text-base">
                  {t("modules.menuflow.description")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-4 flex flex-wrap gap-2">
                  {["Dynamic Pricing", "QR Menus", "POS Integration", "Traffic Analytics"].map((feature) => (
                    <Badge key={feature} variant="secondary">
                      {feature}
                    </Badge>
                  ))}
                </div>
                <Button asChild className="w-full">
                  <Link href="/modules/menuflow">
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* SME App */}
            <Card className="group hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-2xl">{t("modules.smeApp.title")}</CardTitle>
                  <Badge>Live</Badge>
                </div>
                <CardDescription className="text-base">
                  {t("modules.smeApp.description")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-4 flex flex-wrap gap-2">
                  {["Sales Analysis", "Sentiment Tracking", "Competitor Benchmarking", "KPI Dashboards"].map((feature) => (
                    <Badge key={feature} variant="secondary">
                      {feature}
                    </Badge>
                  ))}
                </div>
                <Button asChild className="w-full">
                  <Link href="/modules/sme-app">
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Connecto */}
            <Card className="group hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-2xl">{t("modules.connecto.title")}</CardTitle>
                  <Badge variant="outline">Coming Soon</Badge>
                </div>
                <CardDescription className="text-base">
                  {t("modules.connecto.description")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-4 flex flex-wrap gap-2">
                  {["Voice AI", "Multi-language", "Business Integration", "24/7 Availability"].map((feature) => (
                    <Badge key={feature} variant="secondary">
                      {feature}
                    </Badge>
                  ))}
                </div>
                <Button asChild className="w-full" variant="outline">
                  <Link href="/modules/connecto">
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Event Ticketing */}
            <Card className="group hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-2xl">{t("modules.ticketing.title")}</CardTitle>
                  <Badge variant="outline">Planned</Badge>
                </div>
                <CardDescription className="text-base">
                  {t("modules.ticketing.description")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-4 flex flex-wrap gap-2">
                  {["Dynamic Pricing", "Event Management", "Analytics", "Revenue Optimization"].map((feature) => (
                    <Badge key={feature} variant="secondary">
                      {feature}
                    </Badge>
                  ))}
                </div>
                <Button asChild className="w-full" variant="outline">
                  <Link href="/modules/ticketing">
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Tech Stack Section */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              {t("techStack.title")}
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              {t("techStack.description")}
            </p>
          </div>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {[
              {
                icon: Database,
                title: "Backend",
                description: t("techStack.backend"),
              },
              {
                icon: Smartphone,
                title: "Frontend",
                description: t("techStack.frontend"),
              },
              {
                icon: Brain,
                title: "AI Models",
                description: t("techStack.ai"),
              },
              {
                icon: Cloud,
                title: "Data Sources",
                description: t("techStack.data"),
              },
            ].map((tech, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <tech.icon className="mx-auto h-12 w-12 text-primary" />
                  <CardTitle className="text-lg">{tech.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>{tech.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Quick Start Section */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              {t("quickStart.title")}
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              {t("quickStart.description")}
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-3">
            {[
              {
                step: "1",
                title: t("quickStart.steps.0.title"),
                description: t("quickStart.steps.0.description"),
                icon: Settings,
              },
              {
                step: "2",
                title: t("quickStart.steps.1.title"),
                description: t("quickStart.steps.1.description"),
                icon: Code,
              },
              {
                step: "3",
                title: t("quickStart.steps.2.title"),
                description: t("quickStart.steps.2.description"),
                icon: BarChart3,
              },
            ].map((step, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary text-2xl font-bold text-primary-foreground">
                    {step.step}
                  </div>
                  <CardTitle className="text-xl">{step.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{step.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="mt-12 text-center">
            <Button size="lg" asChild>
              <Link href="/platform">
                Get Started Now
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
