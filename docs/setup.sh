#!/bin/bash

echo "🚀 Setting up SME Analytica Documentation Website..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    echo "Please update Node.js: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if we're in the docs directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this script from the docs directory"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
if command -v npm &> /dev/null; then
    npm install
elif command -v yarn &> /dev/null; then
    yarn install
else
    echo "❌ Neither npm nor yarn found. Please install Node.js with npm."
    exit 1
fi

# Check if installation was successful
if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully!"
    echo ""
    echo "🎉 Setup complete! You can now run:"
    echo ""
    echo "  npm run dev     # Start development server"
    echo "  npm run build   # Build for production"
    echo "  npm run start   # Start production server"
    echo ""
    echo "📖 The documentation will be available at:"
    echo "  http://localhost:3000"
    echo ""
    echo "🌐 Language support:"
    echo "  English: http://localhost:3000/en"
    echo "  Spanish: http://localhost:3000/es"
else
    echo "❌ Installation failed. Please check the error messages above."
    exit 1
fi
