from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from typing import Dict, Any, List, Optional
import uuid
from datetime import datetime, timedelta
import logging
from pydantic import BaseModel, validator

from app.core.schemas.feedback import RestaurantFeedbackCreate, RestaurantFeedbackResponse
from app.core.db.supabase_client import get_supabase_client
from app.api.dependencies import get_current_user
from app.core.engines.sentiment.engine import SentimentAnalysisEngine
from app.core.utils.sentiment_processor import process_restaurant_feedback

logger = logging.getLogger(__name__)
router = APIRouter()

class RestaurantFeedbackCreate(BaseModel):
    restaurant_id: str
    order_id: str
    table_id: Optional[str] = None
    customer_email: Optional[str] = None
    food_rating: Optional[int] = None
    service_rating: Optional[int] = None
    app_rating: Optional[int] = None
    comments: Optional[str] = None
    sentiment_score: Optional[float] = None
    sentiment_magnitude: Optional[float] = None

    @validator('food_rating', 'service_rating', 'app_rating')
    def validate_ratings(cls, v):
        if v is not None and (v < 1 or v > 5):
            raise ValueError('Ratings must be between 1 and 5')
        return v

    @validator('sentiment_score', 'sentiment_magnitude')
    def validate_sentiment(cls, v):
        if v is not None and (v < 0 or v > 1):
            raise ValueError('Sentiment values must be between 0 and 1')
        return v

class RestaurantFeedbackResponse(BaseModel):
    id: str
    restaurant_id: str
    sentiment_score: float
    sentiment_analysis_complete: bool
    message: str

@router.post("/restaurant-feedback", response_model=RestaurantFeedbackResponse)
async def receive_restaurant_feedback(
    feedback: RestaurantFeedbackCreate,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> RestaurantFeedbackResponse:
    """
    Receive customer feedback from restaurant orders and integrate with sentiment analysis.
    
    This endpoint:
    1. Stores feedback in the main SME Analytica database
    2. Processes sentiment analysis if comments are provided
    3. Updates overall business sentiment scores
    4. Triggers AI model improvements in the background
    """
    try:
        supabase = get_supabase_client()
        
        # Calculate sentiment score if not provided
        calculated_sentiment = None
        if feedback.sentiment_score is None and any([
            feedback.food_rating, feedback.service_rating, feedback.app_rating
        ]):
            calculated_sentiment = calculate_sentiment_from_ratings(
                feedback.food_rating,
                feedback.service_rating, 
                feedback.app_rating
            )
            feedback.sentiment_score = calculated_sentiment['score']
            feedback.sentiment_magnitude = calculated_sentiment['magnitude']

        # Store feedback in database
        feedback_data = {
            "restaurant_id": feedback.restaurant_id,
            "order_id": feedback.order_id,
            "table_id": feedback.table_id,
            "customer_email": feedback.customer_email,
            "food_rating": feedback.food_rating,
            "service_rating": feedback.service_rating,
            "app_rating": feedback.app_rating,
            "comments": feedback.comments,
            "sentiment_score": feedback.sentiment_score,
            "sentiment_magnitude": feedback.sentiment_magnitude,
            "source": "menu_flow_dynamo"
        }

        result = supabase.table("restaurant_feedback").insert(feedback_data).execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to store feedback"
            )

        feedback_id = result.data[0]["id"]
        
        # Process sentiment analysis in background
        background_tasks.add_task(
            process_feedback_sentiment_analysis,
            feedback_id,
            feedback.dict(),
            current_user.get("id")
        )

        # Update restaurant sentiment scores in background
        background_tasks.add_task(
            update_restaurant_sentiment_scores,
            feedback.restaurant_id
        )

        return RestaurantFeedbackResponse(
            id=feedback_id,
            restaurant_id=feedback.restaurant_id,
            sentiment_score=feedback.sentiment_score or 0.5,
            sentiment_analysis_complete=feedback.comments is not None,
            message="Feedback received and processing started"
        )

    except Exception as e:
        logger.error(f"Error processing restaurant feedback: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing feedback: {str(e)}"
        )

async def process_feedback_sentiment_analysis(
    feedback_id: str,
    feedback_data: Dict[str, Any],
    user_id: str
) -> None:
    """
    Background task to process sentiment analysis on feedback comments.
    """
    try:
        if not feedback_data.get("comments"):
            return

        # Initialize sentiment analysis engine
        sentiment_engine = SentimentAnalysisEngine()
        
        # Analyze comment sentiment
        sentiment_result = await sentiment_engine.analyze_text(
            feedback_data["comments"],
            context={
                "source": "restaurant_feedback",
                "restaurant_id": feedback_data["restaurant_id"],
                "ratings": {
                    "food": feedback_data.get("food_rating"),
                    "service": feedback_data.get("service_rating"),
                    "app": feedback_data.get("app_rating")
                }
            }
        )

        # Update feedback record with detailed sentiment analysis
        supabase = get_supabase_client()
        supabase.table("restaurant_feedback").update({
            "sentiment_score": sentiment_result.get("score", feedback_data.get("sentiment_score")),
            "sentiment_magnitude": sentiment_result.get("magnitude", feedback_data.get("sentiment_magnitude")),
            "sentiment_aspects": sentiment_result.get("aspects", []),
            "sentiment_keywords": sentiment_result.get("keywords", [])
        }).eq("id", feedback_id).execute()

        logger.info(f"Sentiment analysis completed for feedback {feedback_id}")

    except Exception as e:
        logger.error(f"Error in sentiment analysis processing: {str(e)}")

async def update_restaurant_sentiment_scores(restaurant_id: str) -> None:
    """
    Background task to update overall restaurant sentiment scores.
    """
    try:
        supabase = get_supabase_client()
        
        # Get recent feedback for the restaurant (last 30 days)
        recent_feedback = supabase.table("restaurant_feedback")\
            .select("sentiment_score, sentiment_magnitude, food_rating, service_rating, app_rating, created_at")\
            .eq("restaurant_id", restaurant_id)\
            .gte("created_at", (datetime.now() - timedelta(days=30)).isoformat())\
            .execute()

        if not recent_feedback.data:
            return

        # Calculate aggregated sentiment metrics
        feedback_items = recent_feedback.data
        
        # Overall sentiment score (weighted average)
        sentiment_scores = [f["sentiment_score"] for f in feedback_items if f["sentiment_score"]]
        avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0.5
        
        # Rating averages
        food_ratings = [f["food_rating"] for f in feedback_items if f["food_rating"]]
        service_ratings = [f["service_rating"] for f in feedback_items if f["service_rating"]]
        app_ratings = [f["app_rating"] for f in feedback_items if f["app_rating"]]
        
        avg_food_rating = sum(food_ratings) / len(food_ratings) if food_ratings else None
        avg_service_rating = sum(service_ratings) / len(service_ratings) if service_ratings else None
        avg_app_rating = sum(app_ratings) / len(app_ratings) if app_ratings else None

        # Update restaurant sentiment summary
        sentiment_summary = {
            "restaurant_id": restaurant_id,
            "overall_sentiment_score": avg_sentiment,
            "avg_food_rating": avg_food_rating,
            "avg_service_rating": avg_service_rating,
            "avg_app_rating": avg_app_rating,
            "feedback_count": len(feedback_items),
            "last_updated": datetime.now().isoformat()
        }

        # Upsert into restaurant_sentiment_summary table
        supabase.table("restaurant_sentiment_summary").upsert(
            sentiment_summary, on_conflict=["restaurant_id"]
        ).execute()

        logger.info(f"Updated sentiment scores for restaurant {restaurant_id}")

    except Exception as e:
        logger.error(f"Error updating restaurant sentiment scores: {str(e)}")

def calculate_sentiment_from_ratings(
    food_rating: Optional[int],
    service_rating: Optional[int],
    app_rating: Optional[int]
) -> Dict[str, float]:
    """
    Calculate sentiment score and magnitude from numerical ratings.
    """
    ratings = [r for r in [food_rating, service_rating, app_rating] if r is not None]
    
    if not ratings:
        return {"score": 0.5, "magnitude": 0.1}
    
    # Convert 1-5 scale to 0-1 sentiment score
    avg_rating = sum(ratings) / len(ratings)
    sentiment_score = (avg_rating - 1) / 4
    
    # Magnitude based on number of ratings and consistency
    magnitude = min(0.9, 0.3 + (len(ratings) / 3) * 0.6)
    
    return {
        "score": round(sentiment_score, 3),
        "magnitude": round(magnitude, 3)
    }

@router.get("/restaurant-feedback/{restaurant_id}")
async def get_restaurant_feedback(
    restaurant_id: str,
    limit: int = 50,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """
    Get recent feedback for a specific restaurant.
    """
    try:
        supabase = get_supabase_client()
        
        result = supabase.table("restaurant_feedback")\
            .select("*")\
            .eq("restaurant_id", restaurant_id)\
            .order("created_at", desc=True)\
            .limit(limit)\
            .execute()

        return result.data or []

    except Exception as e:
        logger.error(f"Error fetching restaurant feedback: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching feedback: {str(e)}"
        )

@router.get("/restaurant-sentiment/{restaurant_id}")
async def get_restaurant_sentiment_summary(
    restaurant_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get aggregated sentiment analysis for a restaurant.
    """
    try:
        supabase = get_supabase_client()
        
        result = supabase.table("restaurant_sentiment_summary")\
            .select("*")\
            .eq("restaurant_id", restaurant_id)\
            .single()\
            .execute()

        if not result.data:
            return {
                "restaurant_id": restaurant_id,
                "overall_sentiment_score": 0.5,
                "feedback_count": 0,
                "message": "No sentiment data available"
            }

        return result.data

    except Exception as e:
        logger.error(f"Error fetching restaurant sentiment: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching sentiment data: {str(e)}"
        ) 