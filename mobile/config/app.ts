/**
 * App Configuration for SME Analytica Mobile
 * Handles application identity and context separation
 */

import Constants from 'expo-constants';

export interface AppConfig {
  applicationName: string;
  applicationId: string;
  displayName: string;
  description: string;
  version: string;
  buildNumber: string;
  environment: 'development' | 'staging' | 'production';
}

export interface AppContext {
  applicationName: string;
  applicationId: string;
  userId?: string;
  sessionId: string;
  deviceId: string;
  timestamp: string;
}

/**
 * App Configuration
 */
export const appConfig: AppConfig = {
  applicationName: Constants.expoConfig?.extra?.applicationName || 'sme_analytica',
  applicationId: Constants.expoConfig?.extra?.applicationId || '73e19dba-25df-4c45-949d-e42f19df912a',
  displayName: 'SME Analytica',
  description: 'Business Analytics Platform for Small and Medium Enterprises',
  version: Constants.expoConfig?.version || '1.0.0',
  buildNumber: Constants.expoConfig?.extra?.buildNumber || '1',
  environment: (Constants.expoConfig?.extra?.environment as AppConfig['environment']) || 'development',
};

/**
 * Create app context for database operations
 */
export const createAppContext = (userId?: string): AppContext => ({
  applicationName: appConfig.applicationName,
  applicationId: appConfig.applicationId,
  userId,
  sessionId: generateSessionId(),
  deviceId: getDeviceId(),
  timestamp: new Date().toISOString(),
});

/**
 * Generate unique session ID
 */
function generateSessionId(): string {
  return `${appConfig.applicationName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get device ID (simplified for now)
 */
function getDeviceId(): string {
  // In a real implementation, you'd use a proper device ID library
  return `${appConfig.applicationName}_device_${Math.random().toString(36).substr(2, 16)}`;
}

/**
 * Check if this is the correct application
 */
export const isCurrentApplication = (applicationName: string): boolean => {
  return applicationName === appConfig.applicationName;
};

/**
 * Application constants
 */
export const APP_CONSTANTS = {
  APPLICATION_NAME: appConfig.applicationName,
  APPLICATION_ID: appConfig.applicationId,
  SUPPORTED_APPLICATIONS: ['sme_analytica', 'restaurant_mgmt', 'connecto'],
  DATABASE_PREFIX: 'sme_analytica_mobile',
} as const;

export default appConfig;