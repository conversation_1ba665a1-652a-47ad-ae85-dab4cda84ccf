/**
 * App Context Service for SME Analytica Mobile
 * Handles application identification, session management, and analytics
 */

import { supabase } from '@/integrations/supabase/client';
import { appConfig, createAppContext, type AppContext } from '@/config/app';

export interface AppSession {
  id: string;
  userId: string;
  applicationName: string;
  applicationId: string;
  sessionId: string;
  deviceId?: string;
  sessionStart: string;
  sessionEnd?: string;
  sessionData: Record<string, any>;
}

export interface AppEvent {
  id: string;
  applicationName: string;
  eventName: string;
  eventType: string;
  eventData: Record<string, any>;
  screenName?: string;
  featureName?: string;
  recordedAt: string;
}

export interface AppAnalytics {
  metricName: string;
  metricValue: number;
  metricType?: 'counter' | 'gauge' | 'histogram';
  dimensions?: Record<string, any>;
  tags?: Record<string, any>;
}

class AppContextService {
  private currentSession: AppSession | null = null;
  private appContext: AppContext | null = null;

  /**
   * Initialize app context for the current user
   */
  async initializeAppContext(userId: string): Promise<AppContext> {
    this.appContext = createAppContext(userId);
    
    // Start a new app session
    await this.startAppSession(userId);
    
    return this.appContext;
  }

  /**
   * Start a new app session
   */
  async startAppSession(userId: string, sessionData: Record<string, any> = {}): Promise<string> {
    try {
      if (!this.appContext) {
        this.appContext = createAppContext(userId);
      }

      const { data, error } = await supabase.rpc('start_app_session', {
        p_user_id: userId,
        p_application_name: appConfig.applicationName,
        p_application_id: appConfig.applicationId,
        p_session_id: this.appContext.sessionId,
        p_device_id: this.appContext.deviceId,
        p_session_data: sessionData,
      });

      if (error) {
        console.error('Error starting app session:', error);
        throw error;
      }

      console.log('App session started:', data);
      return data;
    } catch (error) {
      console.error('Failed to start app session:', error);
      throw error;
    }
  }

  /**
   * End the current app session
   */
  async endAppSession(): Promise<boolean> {
    try {
      if (!this.appContext) {
        return false;
      }

      const { data, error } = await supabase.rpc('end_app_session', {
        p_session_id: this.appContext.sessionId,
      });

      if (error) {
        console.error('Error ending app session:', error);
        throw error;
      }

      this.currentSession = null;
      return data;
    } catch (error) {
      console.error('Failed to end app session:', error);
      return false;
    }
  }

  /**
   * Track an app event
   */
  async trackEvent(
    eventName: string,
    eventData: Record<string, any> = {},
    eventType: string = 'user_action',
    screenName?: string,
    featureName?: string
  ): Promise<string | null> {
    try {
      const { data, error } = await supabase.rpc('track_app_event', {
        p_application_name: appConfig.applicationName,
        p_event_name: eventName,
        p_user_id: this.appContext?.userId || null,
        p_application_id: appConfig.applicationId,
        p_session_id: this.appContext?.sessionId || null,
        p_event_type: eventType,
        p_event_data: eventData,
        p_screen_name: screenName,
        p_feature_name: featureName,
      });

      if (error) {
        console.error('Error tracking app event:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Failed to track app event:', error);
      return null;
    }
  }

  /**
   * Record analytics metrics
   */
  async recordAnalytics(analytics: AppAnalytics): Promise<boolean> {
    try {
      const { error } = await supabase.from('app_analytics').insert([
        {
          application_name: appConfig.applicationName,
          application_id: appConfig.applicationId,
          user_id: this.appContext?.userId || null,
          metric_name: analytics.metricName,
          metric_value: analytics.metricValue,
          metric_type: analytics.metricType || 'counter',
          dimensions: analytics.dimensions || {},
          tags: analytics.tags || {},
        },
      ]);

      if (error) {
        console.error('Error recording analytics:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Failed to record analytics:', error);
      return false;
    }
  }

  /**
   * Get user preferences for this app
   */
  async getUserPreferences(): Promise<Record<string, any>> {
    try {
      if (!this.appContext?.userId) {
        return {};
      }

      const { data, error } = await supabase
        .from('user_app_preferences')
        .select('preferences')
        .eq('user_id', this.appContext.userId)
        .eq('application_name', appConfig.applicationName)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error getting user preferences:', error);
        throw error;
      }

      return data?.preferences || {};
    } catch (error) {
      console.error('Failed to get user preferences:', error);
      return {};
    }
  }

  /**
   * Save user preferences for this app
   */
  async saveUserPreferences(preferences: Record<string, any>): Promise<boolean> {
    try {
      if (!this.appContext?.userId) {
        return false;
      }

      const { error } = await supabase.from('user_app_preferences').upsert([
        {
          user_id: this.appContext.userId,
          application_name: appConfig.applicationName,
          application_id: appConfig.applicationId,
          preferences,
        },
      ]);

      if (error) {
        console.error('Error saving user preferences:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Failed to save user preferences:', error);
      return false;
    }
  }

  /**
   * Get app analytics for the current user
   */
  async getAppAnalytics(
    metricNames?: string[],
    dateFrom?: string,
    dateTo?: string
  ): Promise<AppAnalytics[]> {
    try {
      if (!this.appContext?.userId) {
        return [];
      }

      let query = supabase
        .from('app_analytics')
        .select('*')
        .eq('application_name', appConfig.applicationName)
        .eq('user_id', this.appContext.userId);

      if (metricNames && metricNames.length > 0) {
        query = query.in('metric_name', metricNames);
      }

      if (dateFrom) {
        query = query.gte('recorded_at', dateFrom);
      }

      if (dateTo) {
        query = query.lte('recorded_at', dateTo);
      }

      const { data, error } = await query.order('recorded_at', { ascending: false });

      if (error) {
        console.error('Error getting app analytics:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get app analytics:', error);
      return [];
    }
  }

  /**
   * Get current app context
   */
  getAppContext(): AppContext | null {
    return this.appContext;
  }

  /**
   * Check if app context is initialized
   */
  isInitialized(): boolean {
    return this.appContext !== null;
  }

  /**
   * Get application configuration
   */
  getAppConfig() {
    return appConfig;
  }
}

export const appContextService = new AppContextService();
export default appContextService;