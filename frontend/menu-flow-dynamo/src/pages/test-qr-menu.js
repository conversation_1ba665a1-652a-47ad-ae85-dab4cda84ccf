import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { applyDynamicPricing, isDynamicPricingEnabled } from '@/services/dynamicPricingService';
import { getCurrentTraffic } from '@/services/trafficService';
import Head from 'next/head';

// Test restaurant ID (La Pepica Restaurant)
const TEST_RESTAURANT_ID = '1fd5e254-ab0e-468b-adfe-22217536eee6';

export default function TestQrMenu() {
  const [restaurant, setRestaurant] = useState(null);
  const [menuItems, setMenuItems] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(true);
  const [traffic, setTraffic] = useState(null);
  const [isDynamicEnabled, setIsDynamicEnabled] = useState(false);
  const [pricingApplied, setPricingApplied] = useState(false);

  // Fetch restaurant data and menu items
  useEffect(() => {
    async function loadData() {
      setLoading(true);
      
      try {
        // Get restaurant details
        const { data: restaurantData } = await supabase
          .from('restaurants')
          .select('*')
          .eq('id', TEST_RESTAURANT_ID)
          .single();
          
        if (restaurantData) {
          setRestaurant(restaurantData);
          
          // Check if dynamic pricing is enabled
          const isDynamicPricingOn = await isDynamicPricingEnabled(TEST_RESTAURANT_ID);
          setIsDynamicEnabled(isDynamicPricingOn);
          
          // Get current traffic
          const trafficLevel = await getCurrentTraffic(TEST_RESTAURANT_ID);
          setTraffic(trafficLevel);
          
          // Get menu items
          const { data: items } = await supabase
            .from('menu_items')
            .select('*')
            .eq('restaurant_id', TEST_RESTAURANT_ID);
            
          if (items && items.length > 0) {
            // Apply dynamic pricing in customer view
            const pricedItems = await applyDynamicPricing(items, TEST_RESTAURANT_ID, true);
            setMenuItems(pricedItems);
            setPricingApplied(true);
            
            // Extract unique categories
            const uniqueCategories = [...new Set(pricedItems.map(item => item.category))];
            setCategories(uniqueCategories);
          }
        }
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadData();
  }, []);
  
  // Filter menu items by selected category
  const filteredItems = selectedCategory === 'all' 
    ? menuItems 
    : menuItems.filter(item => item.category === selectedCategory);

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>QR Code Menu Test - SME Analytica</title>
      </Head>
      
      {/* Restaurant header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-5xl mx-auto px-4 py-6">
          {restaurant ? (
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">{restaurant.name}</h1>
                <p className="text-sm text-gray-500">{restaurant.address}</p>
              </div>
              {restaurant.logo_url && (
                <img 
                  src={restaurant.logo_url} 
                  alt={`${restaurant.name} logo`} 
                  className="h-16 w-auto object-contain"
                />
              )}
            </div>
          ) : (
            <div className="animate-pulse h-16 bg-gray-200 rounded"></div>
          )}
        </div>
      </header>
      
      {/* Dynamic pricing status */}
      {isDynamicEnabled && traffic !== null && (
        <div className="bg-sky-50 border-b border-sky-100">
          <div className="max-w-5xl mx-auto px-4 py-2 flex items-center justify-between">
            <div className="flex items-center">
              <span className="inline-flex h-2 w-2 rounded-full bg-sky-500 mr-2"></span>
              <span className="text-sm text-sky-700">Dynamic pricing active</span>
            </div>
            <div className="text-sm text-sky-700">
              Current traffic: <span className="font-medium">{Math.round(traffic * 100)}%</span>
            </div>
          </div>
        </div>
      )}
      
      {/* Category filters */}
      <div className="border-b border-gray-200 bg-white sticky top-0 z-10 shadow-sm">
        <div className="max-w-5xl mx-auto px-4">
          <div className="flex overflow-x-auto py-3 space-x-4 no-scrollbar">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-4 py-2 rounded-full text-sm font-medium ${
                selectedCategory === 'all' 
                  ? 'bg-sky-500 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              All
            </button>
            
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap ${
                  selectedCategory === category 
                    ? 'bg-sky-500 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Menu content */}
      <main className="max-w-5xl mx-auto px-4 py-8">
        {loading ? (
          <div className="grid gap-6 md:grid-cols-2">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="animate-pulse bg-white rounded-lg shadow-sm p-4 h-40"></div>
            ))}
          </div>
        ) : (
          <>
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              {selectedCategory === 'all' ? 'All Menu Items' : selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)}
            </h2>
            
            {filteredItems.length === 0 ? (
              <div className="bg-white rounded-lg shadow-sm p-8 text-center">
                <p className="text-gray-500">No items found in this category.</p>
              </div>
            ) : (
              <div className="grid gap-6 md:grid-cols-2">
                {filteredItems.map(item => {
                  const hasDiscount = item.current_price < item.base_price;
                  const hasIncrease = item.current_price > item.base_price;
                  const priceChangePercent = Math.abs(((item.current_price / item.base_price) - 1) * 100).toFixed(1);
                  
                  return (
                    <div key={item.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
                      <div className="flex h-full">
                        {item.image_url && (
                          <div className="w-1/3 bg-gray-200">
                            <img 
                              src={item.image_url} 
                              alt={item.name} 
                              className="h-full w-full object-cover"
                            />
                          </div>
                        )}
                        
                        <div className={`${item.image_url ? 'w-2/3' : 'w-full'} p-4 flex flex-col`}>
                          <div className="flex-grow">
                            <h3 className="font-medium text-gray-900">{item.name}</h3>
                            {item.description && (
                              <p className="mt-1 text-sm text-gray-500 line-clamp-2">{item.description}</p>
                            )}
                          </div>
                          
                          <div className="mt-4 flex items-end justify-between">
                            <div>
                              <div className="font-semibold text-gray-900">
                                ${item.current_price.toFixed(2)}
                              </div>
                              
                              {(hasDiscount || hasIncrease) && pricingApplied && (
                                <div className="flex items-center mt-1">
                                  <div className={`text-xs ${hasDiscount ? 'text-green-600' : 'text-gray-500'} line-through`}>
                                    ${item.base_price.toFixed(2)}
                                  </div>
                                  <div className={`ml-2 text-xs px-1.5 py-0.5 rounded ${
                                    hasDiscount 
                                      ? 'bg-green-100 text-green-800' 
                                      : 'bg-gray-100 text-gray-600'
                                  }`}>
                                    {hasDiscount ? '-' : '+'}{priceChangePercent}%
                                  </div>
                                </div>
                              )}
                            </div>
                            
                            <button className="ml-4 px-3 py-1 bg-sky-500 hover:bg-sky-600 text-white text-sm font-medium rounded">
                              Add
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </>
        )}
      </main>
      
      {/* Testing info footer */}
      <footer className="bg-gray-100 border-t border-gray-200 mt-8">
        <div className="max-w-5xl mx-auto px-4 py-6">
          <div className="bg-white rounded-lg shadow-sm p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Dynamic Pricing Test Info</h3>
            <div className="grid gap-2 text-sm">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600">Dynamic pricing:</div>
                <div className="font-medium">{isDynamicEnabled ? 'Enabled ✅' : 'Disabled ❌'}</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600">Current traffic:</div>
                <div className="font-medium">{traffic !== null ? `${(traffic * 100).toFixed(1)}%` : 'Unknown'}</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600">Pricing applied:</div>
                <div className="font-medium">{pricingApplied ? 'Yes ✅' : 'No ❌'}</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600">Menu items:</div>
                <div className="font-medium">{menuItems.length}</div>
              </div>
              <div className="mt-2 pt-2 border-t border-gray-100">
                <p className="text-gray-600">
                  This is a test page for verifying dynamic pricing in customer view. 
                  Run console test functions to validate all functionality.
                </p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
