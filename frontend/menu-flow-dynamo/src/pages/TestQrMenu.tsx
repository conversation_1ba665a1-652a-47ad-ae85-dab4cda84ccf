import { useState, useEffect } from 'react';
import { supabase } from '../integrations/supabase/client';
import { applyDynamicPricing, isDynamicPricingEnabled, getPricingRules } from '../services/dynamicPricingService';
import { getCurrentTraffic } from '../services/trafficService';

// Import test utility functions
import '../utils/dynamicPricingTester';

// Test restaurant ID (La Pepica Restaurant)
const TEST_RESTAURANT_ID = '1fd5e254-ab0e-468b-adfe-22217536eee6';

const TestQrMenu = () => {
  // Properly typed state with interfaces
  interface Restaurant {
    id: string;
    name: string;
    address: string;
    logo_url?: string;
    dynamic_pricing_enabled: boolean;
    pricing_rules?: Record<string, any>;
  }

  // Use TestMenuItem to avoid conflicts with existing MenuItem interface
  interface TestMenuItem {
    id: string;
    name: string;
    category: string;
    description?: string;
    image_url?: string;
    base_price: number;
    current_price: number;
    price_adjustment_factor: number;
    restaurant_id: string;
  }

  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);
  const [menuItems, setMenuItems] = useState<TestMenuItem[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(true);
  const [traffic, setTraffic] = useState<number | null>(null);
  const [isDynamicEnabled, setIsDynamicEnabled] = useState(false);
  const [pricingApplied, setPricingApplied] = useState(false);

  // Fetch restaurant data and menu items
  useEffect(() => {
    async function loadData() {
      setLoading(true);
      
      try {
        console.log('Initializing mock testing environment for dynamic pricing...');
        
        // Use mock data instead of trying to create database entries
        // This bypasses RLS security restrictions
        const mockRestaurant = createMockRestaurant();
        setRestaurant(mockRestaurant);
        
        // Mock traffic level (high traffic - 85% occupancy)
        const trafficLevel = 0.85;
        setTraffic(trafficLevel);
        console.log('Mock traffic level:', trafficLevel);
        
        // Mock dynamic pricing as enabled
        setIsDynamicEnabled(true);
        
        // Create mock menu items
        const mockMenuItems = createMockMenuItems();
        console.log(`Created ${mockMenuItems.length} mock menu items`);
        
        // Apply dynamic pricing calculation to mock items
        const pricingRules = mockRestaurant.pricing_rules;
        const pricedItems = applyMockDynamicPricing(mockMenuItems, trafficLevel, pricingRules);
        
        setMenuItems(pricedItems);
        setPricingApplied(true);
        console.log('Dynamic pricing applied to mock menu items');
        
        // Extract categories from mock items
        const uniqueCategories = [...new Set(pricedItems.map(item => item.category))];
        setCategories(uniqueCategories);
        console.log('Categories:', uniqueCategories);
      } catch (error) {
        console.error('Error initializing mock data:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadData();
  }, []);
  
  /**
   * Helper function to ensure the test restaurant has dynamic pricing enabled
   */
  /**
   * Create a mock restaurant for testing without database access
   */
  function createMockRestaurant(): Restaurant {
    // Define pricing rules that match our dynamic pricing needs
    const pricingRules = {
      updatedAt: new Date().toISOString(),
      applyToCategories: { food: true, drinks: true },
      highTraffic: { threshold: 0.7, percentage: 10 },
      mediumTraffic: { threshold: 0.4, percentage: 5 },
      lowTraffic: { threshold: 0.2, percentage: -5 },
      confidenceThreshold: 0.6
    };
    
    return {
      id: TEST_RESTAURANT_ID,
      name: 'La Pepica Restaurant',
      address: 'Test Address, Valencia, Spain',
      dynamic_pricing_enabled: true,
      pricing_rules: pricingRules,
      logo_url: 'https://via.placeholder.com/150?text=La+Pepica'
    };
  }
  
  /**
   * Create mock menu items for testing without database access
   */
  function createMockMenuItems(): TestMenuItem[] {
    return [
      {
        id: '1',
        restaurant_id: TEST_RESTAURANT_ID,
        name: 'Paella Valenciana',
        category: 'food',
        description: 'Traditional Spanish rice dish with chicken, rabbit, and vegetables',
        base_price: 22.99,
        current_price: 22.99,
        price_adjustment_factor: 1.0,
        image_url: 'https://via.placeholder.com/150?text=Paella'
      },
      {
        id: '2',
        restaurant_id: TEST_RESTAURANT_ID,
        name: 'Sangria',
        category: 'drinks',
        description: 'Refreshing wine punch with fresh fruits',
        base_price: 7.50,
        current_price: 7.50,
        price_adjustment_factor: 1.0,
        image_url: 'https://via.placeholder.com/150?text=Sangria'
      },
      {
        id: '3',
        restaurant_id: TEST_RESTAURANT_ID,
        name: 'Gazpacho',
        category: 'food',
        description: 'Cold soup made of raw, blended vegetables',
        base_price: 8.99,
        current_price: 8.99,
        price_adjustment_factor: 1.0,
        image_url: 'https://via.placeholder.com/150?text=Gazpacho'
      },
      {
        id: '4',
        restaurant_id: TEST_RESTAURANT_ID,
        name: 'Tinto de Verano',
        category: 'drinks',
        description: 'Summer red wine with lemon soda',
        base_price: 6.50,
        current_price: 6.50,
        price_adjustment_factor: 1.0,
        image_url: 'https://via.placeholder.com/150?text=Tinto'
      },
      {
        id: '5',
        restaurant_id: TEST_RESTAURANT_ID,
        name: 'Patatas Bravas',
        category: 'food',
        description: 'Fried potatoes with spicy tomato sauce',
        base_price: 5.99,
        current_price: 5.99,
        price_adjustment_factor: 1.0,
        image_url: 'https://via.placeholder.com/150?text=Patatas'
      },
      {
        id: '6',
        restaurant_id: TEST_RESTAURANT_ID,
        name: 'Cava',
        category: 'drinks',
        description: 'Spanish sparkling wine',
        base_price: 8.99,
        current_price: 8.99,
        price_adjustment_factor: 1.0,
        image_url: 'https://via.placeholder.com/150?text=Cava'
      }
    ];
  }
  
  /**
   * Apply dynamic pricing calculations to mock menu items
   * This simulates the actual dynamicPricingService without database dependencies
   */
  function applyMockDynamicPricing(items: TestMenuItem[], trafficLevel: number, rules: any): TestMenuItem[] {
    // Calculate price factor based on traffic level
    let priceFactor = 1.0;
    
    if (trafficLevel >= (rules?.highTraffic?.threshold || 0.7)) {
      // High traffic - ALWAYS INCREASE prices
      const percentage = Math.abs(rules?.highTraffic?.percentage || 10);
      priceFactor = 1 + (percentage / 100);
      console.log(`HIGH TRAFFIC (${Math.round(trafficLevel * 100)}%) - Price factor: ${priceFactor.toFixed(2)}`);
    } else if (trafficLevel >= (rules?.mediumTraffic?.threshold || 0.4)) {
      // Medium traffic - slight increases
      const percentage = Math.abs(rules?.mediumTraffic?.percentage || 5);
      priceFactor = 1 + (percentage / 100);
      console.log(`MEDIUM TRAFFIC (${Math.round(trafficLevel * 100)}%) - Price factor: ${priceFactor.toFixed(2)}`);
    } else if (trafficLevel <= (rules?.lowTraffic?.threshold || 0.2)) {
      // Low traffic - consider a discount
      const percentage = Math.abs(rules?.lowTraffic?.percentage || 5);
      
      // Check if we should decrease (negative percentage) or increase prices
      if ((rules?.lowTraffic?.percentage || 0) < 0) {
        priceFactor = 1 - (percentage / 100); // Discount
        console.log(`LOW TRAFFIC (${Math.round(trafficLevel * 100)}%) - Price factor: ${priceFactor.toFixed(2)} (DISCOUNT)`);
      } else {
        priceFactor = 1 + (percentage / 100); // Increase
        console.log(`LOW TRAFFIC (${Math.round(trafficLevel * 100)}%) - Price factor: ${priceFactor.toFixed(2)} (INCREASE)`);
      }
    } else {
      console.log(`NORMAL TRAFFIC (${Math.round(trafficLevel * 100)}%) - No price adjustment`);
    }
    
    // Apply price factor to each item based on category settings
    return items.map(item => {
      const newItem = { ...item };
      
      // Check if we should apply pricing to this item category
      const applyToFood = rules?.applyToCategories?.food === true;
      const applyToDrinks = rules?.applyToCategories?.drinks === true;
      
      const isFood = item.category === 'food';
      const isDrink = item.category === 'drinks';
      
      let shouldApplyPricing = false;
      
      if (isDrink) {
        shouldApplyPricing = applyToDrinks;
      } else if (isFood) {
        shouldApplyPricing = applyToFood;
      }
      
      if (shouldApplyPricing) {
        // Apply dynamic pricing
        newItem.price_adjustment_factor = priceFactor;
        newItem.current_price = Math.round((item.base_price * priceFactor) * 100) / 100; // Round to nearest cent
      }
      
      return newItem;
    });
  }
  
  // Filter menu items by selected category
  const filteredItems = selectedCategory === 'all' 
    ? menuItems 
    : menuItems.filter(item => item.category === selectedCategory);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Restaurant header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-5xl mx-auto px-4 py-6">
          {restaurant ? (
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">{restaurant.name}</h1>
                <p className="text-sm text-gray-500">{restaurant.address}</p>
              </div>
              {restaurant.logo_url && (
                <img 
                  src={restaurant.logo_url} 
                  alt={`${restaurant.name} logo`} 
                  className="h-16 w-auto object-contain"
                />
              )}
            </div>
          ) : (
            <div className="animate-pulse h-16 bg-gray-200 rounded"></div>
          )}
        </div>
      </header>
      
      {/* Dynamic pricing status */}
      {isDynamicEnabled && traffic !== null && (
        <div className="bg-sky-50 border-b border-sky-100">
          <div className="max-w-5xl mx-auto px-4 py-2 flex items-center justify-between">
            <div className="flex items-center">
              <span className="inline-flex h-2 w-2 rounded-full bg-sky-500 mr-2"></span>
              <span className="text-sm text-sky-700">Dynamic pricing active</span>
            </div>
            <div className="text-sm text-sky-700">
              Current traffic: <span className="font-medium">{Math.round((traffic || 0) * 100)}%</span>
            </div>
          </div>
        </div>
      )}
      
      {/* Category filters */}
      <div className="border-b border-gray-200 bg-white sticky top-0 z-10 shadow-sm">
        <div className="max-w-5xl mx-auto px-4">
          <div className="flex overflow-x-auto py-3 space-x-4 no-scrollbar">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-4 py-2 rounded-full text-sm font-medium ${
                selectedCategory === 'all' 
                  ? 'bg-sky-500 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              All
            </button>
            
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap ${
                  selectedCategory === category 
                    ? 'bg-sky-500 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Menu content */}
      <main className="max-w-5xl mx-auto px-4 py-8">
        {loading ? (
          <div className="grid gap-6 md:grid-cols-2">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="animate-pulse bg-white rounded-lg shadow-sm p-4 h-40"></div>
            ))}
          </div>
        ) : (
          <>
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              {selectedCategory === 'all' ? 'All Menu Items' : selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)}
            </h2>
            
            {filteredItems.length === 0 ? (
              <div className="bg-white rounded-lg shadow-sm p-8 text-center">
                <p className="text-gray-500">No items found in this category.</p>
              </div>
            ) : (
              <div className="grid gap-6 md:grid-cols-2">
                {filteredItems.map(item => {
                  const hasDiscount = item.current_price < item.base_price;
                  const hasIncrease = item.current_price > item.base_price;
                  const priceChangePercent = Math.abs(((item.current_price / item.base_price) - 1) * 100).toFixed(1);
                  
                  return (
                    <div key={item.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
                      <div className="flex h-full">
                        {item.image_url && (
                          <div className="w-1/3 bg-gray-200">
                            <img 
                              src={item.image_url} 
                              alt={item.name} 
                              className="h-full w-full object-cover"
                            />
                          </div>
                        )}
                        
                        <div className={`${item.image_url ? 'w-2/3' : 'w-full'} p-4 flex flex-col`}>
                          <div className="flex-grow">
                            <h3 className="font-medium text-gray-900">{item.name}</h3>
                            {item.description && (
                              <p className="mt-1 text-sm text-gray-500 line-clamp-2">{item.description}</p>
                            )}
                          </div>
                          
                          <div className="mt-4 flex items-end justify-between">
                            <div>
                              <div className="font-semibold text-gray-900">
                                ${item.current_price.toFixed(2)}
                              </div>
                              
                              {(hasDiscount || hasIncrease) && pricingApplied && (
                                <div className="flex items-center mt-1">
                                  <div className={`text-xs ${hasDiscount ? 'text-green-600' : 'text-gray-500'} line-through`}>
                                    ${item.base_price.toFixed(2)}
                                  </div>
                                  <div className={`ml-2 text-xs px-1.5 py-0.5 rounded ${
                                    hasDiscount 
                                      ? 'bg-green-100 text-green-800' 
                                      : 'bg-gray-100 text-gray-600'
                                  }`}>
                                    {hasDiscount ? '-' : '+'}{priceChangePercent}%
                                  </div>
                                </div>
                              )}
                            </div>
                            
                            <button className="ml-4 px-3 py-1 bg-sky-500 hover:bg-sky-600 text-white text-sm font-medium rounded">
                              Add
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </>
        )}
      </main>
      
      {/* Testing info footer */}
      <footer className="bg-gray-100 border-t border-gray-200 mt-8">
        <div className="max-w-5xl mx-auto px-4 py-6">
          <div className="bg-white rounded-lg shadow-sm p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Dynamic Pricing Test Info</h3>
            <div className="grid gap-2 text-sm">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600">Dynamic pricing:</div>
                <div className="font-medium">{isDynamicEnabled ? 'Enabled ✅' : 'Disabled ❌'}</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600">Current traffic:</div>
                <div className="font-medium">{traffic !== null ? `${(traffic * 100).toFixed(1)}%` : 'Unknown'}</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600">Pricing applied:</div>
                <div className="font-medium">{pricingApplied ? 'Yes ✅' : 'No ❌'}</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600">Menu items:</div>
                <div className="font-medium">{menuItems.length}</div>
              </div>
              <div className="mt-2 pt-2 border-t border-gray-100">
                <p className="text-gray-600">
                  This is a test page for verifying dynamic pricing in customer view. 
                  Run console test functions to validate all functionality.
                </p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default TestQrMenu;
