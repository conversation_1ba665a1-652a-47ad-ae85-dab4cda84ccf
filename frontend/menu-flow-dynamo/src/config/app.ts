/**
 * App Configuration for Restaurant Management System
 * Handles application identity and context separation
 */

export interface AppConfig {
  applicationName: string;
  applicationId: string;
  displayName: string;
  description: string;
  version: string;
  environment: 'development' | 'staging' | 'production';
}

export interface AppContext {
  applicationName: string;
  applicationId: string;
  userId?: string;
  restaurantId?: string;
  sessionId: string;
  timestamp: string;
}

/**
 * App Configuration
 */
export const appConfig: AppConfig = {
  applicationName: import.meta.env.VITE_APPLICATION_NAME || 'restaurant_mgmt',
  applicationId: import.meta.env.VITE_APPLICATION_ID || '4a631768-6241-4d04-9066-932cd22350e5',
  displayName: 'Restaurant Management System',
  description: 'Complete restaurant operations and ordering platform',
  version: '1.0.0',
  environment: (import.meta.env.NODE_ENV as AppConfig['environment']) || 'development',
};

/**
 * Create app context for database operations
 */
export const createAppContext = (userId?: string, restaurantId?: string): AppContext => ({
  applicationName: appConfig.applicationName,
  applicationId: appConfig.applicationId,
  userId,
  restaurantId,
  sessionId: generateSessionId(),
  timestamp: new Date().toISOString(),
});

/**
 * Generate unique session ID
 */
function generateSessionId(): string {
  return `${appConfig.applicationName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Check if this is the correct application
 */
export const isCurrentApplication = (applicationName: string): boolean => {
  return applicationName === appConfig.applicationName;
};

/**
 * Application constants
 */
export const APP_CONSTANTS = {
  APPLICATION_NAME: appConfig.applicationName,
  APPLICATION_ID: appConfig.applicationId,
  SUPPORTED_APPLICATIONS: ['sme_analytica', 'restaurant_mgmt', 'connecto'],
  DATABASE_PREFIX: 'restaurant_mgmt',
  FEATURE_FLAGS: {
    DYNAMIC_PRICING: import.meta.env.VITE_ENABLE_DYNAMIC_PRICING === 'true',
    ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
    MOCK_DATA: import.meta.env.VITE_ENABLE_MOCK_DATA === 'true',
  }
} as const;

export default appConfig;