/**
 * Dynamic Pricing Testing Utility
 * 
 * This utility provides comprehensive tests for the dynamic pricing system.
 * Run these functions from the browser console to verify functionality.
 */

// Test restaurant ID (La Pepica Restaurant)
const TEST_RESTAURANT_ID = '1fd5e254-ab0e-468b-adfe-22217536eee6';

/**
 * Main test runner - executes all dynamic pricing tests
 */
async function testDynamicPricing() {
  console.log('🧪 STARTING DYNAMIC PRICING TEST SUITE');
  console.log('======================================');
  
  try {
    // Test 1: Verify dynamic pricing is enabled
    await testDynamicPricingEnabled();
    
    // Test 2: Verify pricing rules are complete
    await testPricingRules();
    
    // Test 3: Test different traffic scenarios
    await testTrafficScenarios();
    
    // Test 4: Test category-specific pricing
    await testCategoryPricing();
    
    // Test 5: Test customer view
    await testCustomerView();
    
    console.log('\n✅ ALL TESTS COMPLETED SUCCESSFULLY');
    return true;
  } catch (error) {
    console.error('❌ TEST SUITE FAILED:', error);
    return false;
  }
}

/**
 * Test 1: Verify dynamic pricing is enabled for the restaurant
 */
async function testDynamicPricingEnabled() {
  console.log('\n1️⃣ TESTING DYNAMIC PRICING ENABLED');
  console.log('----------------------------------');
  
  try {
    // Import dynamicPricingService from the window object
    const { isDynamicPricingEnabled, setDynamicPricingEnabled } = window.require('@/services/dynamicPricingService');
    
    // First, ensure dynamic pricing is enabled
    await setDynamicPricingEnabled(true, TEST_RESTAURANT_ID);
    console.log('  Dynamic pricing set to ENABLED');
    
    // Then verify it's enabled
    const isEnabled = await isDynamicPricingEnabled(TEST_RESTAURANT_ID);
    console.log(`  Dynamic pricing enabled: ${isEnabled}`);
    
    if (!isEnabled) {
      throw new Error('Dynamic pricing is not enabled for test restaurant');
    }
    
    console.log('  ✅ Dynamic pricing is correctly enabled');
    return true;
  } catch (error) {
    console.error('  ❌ Test failed:', error);
    throw error;
  }
}

/**
 * Test 2: Verify pricing rules are complete
 */
async function testPricingRules() {
  console.log('\n2️⃣ TESTING PRICING RULES');
  console.log('------------------------');
  
  try {
    const { getPricingRules } = window.require('@/services/dynamicPricingService');
    const { supabase } = window.require('@/integrations/supabase/client');
    
    // First, ensure the restaurant has complete pricing rules
    const completeRules = {
      updatedAt: new Date().toISOString(),
      applyToCategories: { food: true, drinks: true },
      highTraffic: { threshold: 0.7, percentage: 10 },
      mediumTraffic: { threshold: 0.4, percentage: 5 },
      lowTraffic: { threshold: 0.2, percentage: -5 },
      confidenceThreshold: 0.6
    };
    
    console.log('  Setting complete pricing rules...');
    const { error: updateError } = await supabase
      .from('restaurants')
      .update({ 
        pricing_rules: completeRules,
        dynamic_pricing_enabled: true
      })
      .eq('id', TEST_RESTAURANT_ID);
      
    if (updateError) {
      throw new Error(`Failed to update pricing rules: ${updateError.message}`);
    }
    
    // Verify the rules are set correctly
    const rules = await getPricingRules(TEST_RESTAURANT_ID, null, true);
    
    console.log('  Retrieved rules:', rules);
    
    // Validate rules
    if (!rules.highTraffic || !rules.mediumTraffic || !rules.lowTraffic) {
      throw new Error('Pricing rules are incomplete');
    }
    
    if (!rules.applyToCategories || typeof rules.applyToCategories.food !== 'boolean') {
      throw new Error('Category settings are missing or incorrect');
    }
    
    console.log('  ✅ Pricing rules are correctly configured');
    return true;
  } catch (error) {
    console.error('  ❌ Test failed:', error);
    throw error;
  }
}

/**
 * Test 3: Test different traffic scenarios
 */
async function testTrafficScenarios() {
  console.log('\n3️⃣ TESTING TRAFFIC SCENARIOS');
  console.log('----------------------------');
  
  try {
    const { applyDynamicPricing } = window.require('@/services/dynamicPricingService');
    const { supabase } = window.require('@/integrations/supabase/client');
    
    // Get some menu items for testing
    const { data: menuItems, error: menuError } = await supabase
      .from('menu_items')
      .select('*')
      .eq('restaurant_id', TEST_RESTAURANT_ID)
      .limit(5);
      
    if (menuError || !menuItems?.length) {
      throw new Error(`Failed to fetch menu items: ${menuError?.message || 'No items found'}`);
    }
    
    console.log(`  Found ${menuItems.length} menu items for testing`);
    
    // Test scenarios
    const scenarios = [
      { name: 'High Traffic', traffic: 0.85, expectedIncrease: true },
      { name: 'Medium Traffic', traffic: 0.55, expectedIncrease: true },
      { name: 'Low Traffic', traffic: 0.15, expectedIncrease: false },
      { name: 'Normal Traffic', traffic: 0.35, expectedIncrease: false }
    ];
    
    for (const scenario of scenarios) {
      console.log(`\n  Testing ${scenario.name} (${scenario.traffic * 100}% occupancy):`);
      
      // Create traffic record in database
      await createTrafficRecord(TEST_RESTAURANT_ID, scenario.traffic);
      
      // Apply dynamic pricing
      const updatedItems = await applyDynamicPricing([...menuItems], TEST_RESTAURANT_ID, false);
      
      // Check if prices changed as expected
      const priceChanges = updatedItems.map(item => {
        const originalPrice = item.base_price;
        const newPrice = item.current_price;
        const percentChange = ((newPrice / originalPrice) - 1) * 100;
        
        return {
          name: item.name,
          originalPrice,
          newPrice,
          percentChange: Number(percentChange.toFixed(1)),
          increased: newPrice > originalPrice,
          decreased: newPrice < originalPrice,
          unchanged: newPrice === originalPrice
        };
      });
      
      console.log('  Price changes:');
      console.table(priceChanges.map(({ name, originalPrice, newPrice, percentChange }) => ({
        item: name,
        original: `$${originalPrice.toFixed(2)}`,
        new: `$${newPrice.toFixed(2)}`,
        change: `${percentChange > 0 ? '+' : ''}${percentChange}%`
      })));
      
      // Verify expected behavior
      if (scenario.expectedIncrease) {
        const anyIncreased = priceChanges.some(change => change.increased);
        if (!anyIncreased) {
          console.warn(`  ⚠️ Expected price increases for ${scenario.name} but none occurred`);
        } else {
          console.log(`  ✅ Prices correctly increased for ${scenario.name}`);
        }
      } else if (scenario.name === 'Low Traffic') {
        const anyDecreased = priceChanges.some(change => change.decreased);
        if (!anyDecreased) {
          console.warn(`  ⚠️ Expected price decreases for ${scenario.name} but none occurred`);
        } else {
          console.log(`  ✅ Prices correctly decreased for ${scenario.name}`);
        }
      } else {
        console.log(`  ✅ Pricing behavior validated for ${scenario.name}`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('  ❌ Test failed:', error);
    throw error;
  }
}

/**
 * Test 4: Test category-specific pricing
 */
async function testCategoryPricing() {
  console.log('\n4️⃣ TESTING CATEGORY-SPECIFIC PRICING');
  console.log('-----------------------------------');
  
  try {
    const { applyDynamicPricing } = window.require('@/services/dynamicPricingService');
    const { supabase } = window.require('@/integrations/supabase/client');
    
    // Get menu items for testing (both food and drinks)
    const { data: menuItems, error: menuError } = await supabase
      .from('menu_items')
      .select('*')
      .eq('restaurant_id', TEST_RESTAURANT_ID)
      .or('category.eq.food,category.eq.drinks');
      
    if (menuError || !menuItems?.length) {
      throw new Error(`Failed to fetch menu items: ${menuError?.message || 'No items found'}`);
    }
    
    // Group items by category
    const foodItems = menuItems.filter(item => item.category === 'food');
    const drinkItems = menuItems.filter(item => item.category === 'drinks');
    
    console.log(`  Found ${foodItems.length} food items and ${drinkItems.length} drink items`);
    
    // Create high traffic to ensure price changes
    await createTrafficRecord(TEST_RESTAURANT_ID, 0.85);
    
    // Test scenarios
    const scenarios = [
      { name: 'Both Categories', food: true, drinks: true },
      { name: 'Food Only', food: true, drinks: false },
      { name: 'Drinks Only', food: false, drinks: true },
      { name: 'Neither Category', food: false, drinks: false }
    ];
    
    for (const scenario of scenarios) {
      console.log(`\n  Testing ${scenario.name}:`);
      
      // Update pricing rules
      const rules = {
        updatedAt: new Date().toISOString(),
        applyToCategories: { 
          food: scenario.food, 
          drinks: scenario.drinks 
        },
        highTraffic: { threshold: 0.7, percentage: 10 },
        mediumTraffic: { threshold: 0.4, percentage: 5 },
        lowTraffic: { threshold: 0.2, percentage: -5 }
      };
      
      const { error: updateError } = await supabase
        .from('restaurants')
        .update({ pricing_rules: rules })
        .eq('id', TEST_RESTAURANT_ID);
        
      if (updateError) {
        throw new Error(`Failed to update category settings: ${updateError.message}`);
      }
      
      // Apply dynamic pricing
      const updatedItems = await applyDynamicPricing([...menuItems], TEST_RESTAURANT_ID, false);
      
      // Check if prices changed as expected
      const foodChanged = updatedItems
        .filter(item => item.category === 'food')
        .some(item => item.current_price !== item.base_price);
        
      const drinksChanged = updatedItems
        .filter(item => item.category === 'drinks')
        .some(item => item.current_price !== item.base_price);
      
      console.log(`  Food prices changed: ${foodChanged}`);
      console.log(`  Drink prices changed: ${drinksChanged}`);
      
      // Verify expected behavior
      if (scenario.food !== foodChanged) {
        console.warn(`  ⚠️ Food pricing did not behave as expected (enabled: ${scenario.food}, changed: ${foodChanged})`);
      }
      
      if (scenario.drinks !== drinksChanged) {
        console.warn(`  ⚠️ Drink pricing did not behave as expected (enabled: ${scenario.drinks}, changed: ${drinksChanged})`);
      }
      
      if ((scenario.food === foodChanged) && (scenario.drinks === drinksChanged)) {
        console.log(`  ✅ Category pricing correctly applied for ${scenario.name}`);
      }
    }
    
    // Reset to apply to both categories for future tests
    await supabase
      .from('restaurants')
      .update({ 
        pricing_rules: {
          updatedAt: new Date().toISOString(),
          applyToCategories: { food: true, drinks: true },
          highTraffic: { threshold: 0.7, percentage: 10 },
          mediumTraffic: { threshold: 0.4, percentage: 5 },
          lowTraffic: { threshold: 0.2, percentage: -5 }
        }
      })
      .eq('id', TEST_RESTAURANT_ID);
      
    return true;
  } catch (error) {
    console.error('  ❌ Test failed:', error);
    throw error;
  }
}

/**
 * Test 5: Test customer view
 */
async function testCustomerView() {
  console.log('\n5️⃣ TESTING CUSTOMER VIEW');
  console.log('------------------------');
  
  try {
    const { applyDynamicPricing } = window.require('@/services/dynamicPricingService');
    const { supabase } = window.require('@/integrations/supabase/client');
    
    // Get menu items for testing
    const { data: menuItems, error: menuError } = await supabase
      .from('menu_items')
      .select('*')
      .eq('restaurant_id', TEST_RESTAURANT_ID)
      .limit(5);
      
    if (menuError || !menuItems?.length) {
      throw new Error(`Failed to fetch menu items: ${menuError?.message || 'No items found'}`);
    }
    
    // Create high traffic
    await createTrafficRecord(TEST_RESTAURANT_ID, 0.85);
    
    // Test admin view first
    console.log('  Testing Admin View:');
    const adminItems = await applyDynamicPricing([...menuItems], TEST_RESTAURANT_ID, false);
    
    // Test customer view
    console.log('  Testing Customer View:');
    const customerItems = await applyDynamicPricing([...menuItems], TEST_RESTAURANT_ID, true);
    
    // Compare admin and customer views
    const comparison = adminItems.map((adminItem, index) => {
      const customerItem = customerItems[index];
      
      return {
        name: adminItem.name,
        adminPrice: adminItem.current_price,
        customerPrice: customerItem.current_price,
        match: adminItem.current_price === customerItem.current_price
      };
    });
    
    console.log('  Admin vs Customer View Comparison:');
    console.table(comparison.map(({ name, adminPrice, customerPrice, match }) => ({
      item: name,
      adminPrice: `$${adminPrice.toFixed(2)}`,
      customerPrice: `$${customerPrice.toFixed(2)}`,
      match: match ? '✅' : '❌'
    })));
    
    // Verify prices match
    const allMatch = comparison.every(item => item.match);
    
    if (allMatch) {
      console.log('  ✅ Customer view correctly shows the same prices as admin view');
    } else {
      console.warn('  ⚠️ Customer view prices differ from admin view prices');
    }
    
    return true;
  } catch (error) {
    console.error('  ❌ Test failed:', error);
    throw error;
  }
}

/**
 * Helper function to create a traffic record for testing
 */
async function createTrafficRecord(restaurantId, occupancyPercentage) {
  try {
    const { supabase } = window.require('@/integrations/supabase/client');
    
    // Get restaurant tables to calculate active/total tables
    const { data: tables } = await supabase
      .from('restaurant_tables')
      .select('id')
      .eq('restaurant_id', restaurantId);
      
    const totalTables = tables?.length || 10;
    const activeTables = Math.round(totalTables * occupancyPercentage);
    
    // Insert traffic record
    const { error } = await supabase
      .from('traffic_heatmap')
      .insert({
        restaurant_id: restaurantId,
        timestamp: new Date().toISOString(),
        day_of_week: new Date().getDay(),
        hour_of_day: new Date().getHours(),
        occupancy_percentage: occupancyPercentage,
        active_tables: activeTables,
        total_tables: totalTables,
        source: 'test',
        confidence: 0.95
      });
      
    if (error) {
      console.error('Failed to create traffic record:', error);
      return false;
    }
    
    console.log(`  Created traffic record: ${(occupancyPercentage * 100).toFixed(0)}% occupancy (${activeTables}/${totalTables} tables)`);
    return true;
  } catch (error) {
    console.error('Error creating traffic record:', error);
    return false;
  }
}

/**
 * Make tests available globally
 */
if (typeof window !== 'undefined') {
  // Make test restaurant ID available globally
  window.TEST_RESTAURANT_ID = TEST_RESTAURANT_ID;
  
  // Make createTrafficRecord available for manual testing
  window.createTrafficRecord = createTrafficRecord;
  
  // Main test function
  window.testDynamicPricing = testDynamicPricing;
  
  // Individual test functions for granular testing
  window.testDynamicPricingEnabled = testDynamicPricingEnabled;
  window.testPricingRules = testPricingRules;
  window.testTrafficScenarios = testTrafficScenarios;
  window.testCategoryPricing = testCategoryPricing;
  window.testCustomerView = testCustomerView;
  
  console.log('%c Dynamic Pricing Test Suite', 'background: #0ea5e9; color: white; font-weight: bold; padding: 5px 10px; border-radius: 3px;');
  console.log('%c Available test functions:', 'font-weight: bold;');
  console.log('%c • window.testDynamicPricing()', 'color: #0ea5e9;');
  console.log('%c • window.testTrafficScenarios()', 'color: #0ea5e9;');
  console.log('%c • window.testCategoryPricing()', 'color: #0ea5e9;');
  console.log('%c • window.testCustomerView()', 'color: #0ea5e9;');
  console.log('%c • window.createTrafficRecord(occupancyPercentage)', 'color: #0ea5e9;');
}

// Export for module usage
export {
  testDynamicPricing,
  testDynamicPricingEnabled,
  testPricingRules,
  testTrafficScenarios,
  testCategoryPricing,
  testCustomerView
};
