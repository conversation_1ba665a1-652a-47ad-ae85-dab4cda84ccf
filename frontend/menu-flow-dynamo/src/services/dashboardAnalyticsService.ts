import { supabase } from '@/integrations/supabase/client';
import { getCurrentTraffic, getTrafficData } from './trafficService';

export interface DashboardMetrics {
  todayOrders: number;
  todayRevenue: number;
  avgOrderValue: number;
  currentTraffic: number;
  trafficStatus: 'low' | 'normal' | 'high';
  popularItems: PopularItem[];
  recentOrders: RecentOrder[];
  hourlyStats: HourlyStats[];
}

export interface PopularItem {
  id: string;
  name: string;
  category: string;
  orderCount: number;
  totalSales: number;
  currentPrice: number;
}

export interface RecentOrder {
  id: string;
  customerName?: string;
  totalAmount: number;
  status: string;
  createdAt: string;
  itemCount: number;
}

export interface HourlyStats {
  hour: number;
  orderCount: number;
  revenue: number;
}

export interface TrafficData {
  currentReading: number;
  location: string;
  sensorType: string;
  status: string;
  lastUpdated: string;
}

/**
 * Fetch comprehensive dashboard metrics for a restaurant
 */
export async function fetchDashboardMetrics(restaurantId: string): Promise<DashboardMetrics> {
  try {
    console.log('📊 Dashboard Analytics - Starting fetch for restaurant:', restaurantId);

    // Fetch today's orders and revenue with proper timezone handling
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString();
    const tomorrowStart = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1).toISOString();
    
    console.log('📊 Date range for today:', { todayStart, tomorrowStart });

    const { data: todayStats, error: todayError } = await supabase
      .from('orders')
      .select('total_amount, created_at')
      .eq('restaurant_id', restaurantId)
      .gte('created_at', todayStart)
      .lt('created_at', tomorrowStart);

    if (todayError) {
      console.error('📊 Error fetching today stats:', todayError);
      throw todayError;
    }

    console.log('📊 Today stats raw data:', todayStats);

    const todayOrders = todayStats?.length || 0;
    const todayRevenue = todayStats?.reduce((sum, order) => sum + (parseFloat(order.total_amount) || 0), 0) || 0;
    const avgOrderValue = todayOrders > 0 ? todayRevenue / todayOrders : 0;

    console.log('📊 Today metrics:', { todayOrders, todayRevenue, avgOrderValue });

    // Fetch current traffic data using the proper traffic service
    const currentTrafficValue = await getCurrentTraffic(restaurantId);
    const currentTraffic = Math.round(currentTrafficValue * 100); // Convert to percentage
    const trafficStatus: 'low' | 'normal' | 'high' = 
      currentTraffic < 30 ? 'low' : currentTraffic > 70 ? 'high' : 'normal';

    console.log('📊 Dashboard Analytics - Traffic Data:', {
      restaurantId,
      currentTrafficValue,
      currentTraffic,
      trafficStatus
    });

    // Fetch popular items from menu sales analytics (last 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    console.log('📊 Fetching popular items since:', sevenDaysAgo);

    const { data: popularItemsData, error: popularError } = await supabase
      .from('menu_sales_analytics')
      .select('menu_item_id, item_name, category, current_price, order_count, total_sales')
      .eq('restaurant_id', restaurantId)
      .gte('order_date', sevenDaysAgo)
      .order('order_count', { ascending: false })
      .limit(5);

    if (popularError) {
      console.error('📊 Error fetching popular items:', popularError);
      throw popularError;
    }

    console.log('📊 Popular items raw data:', popularItemsData);

    const popularItems: PopularItem[] = popularItemsData?.map(item => ({
      id: item.menu_item_id,
      name: item.item_name,
      category: item.category,
      orderCount: item.order_count,
      totalSales: parseFloat(item.total_sales),
      currentPrice: parseFloat(item.current_price)
    })) || [];

    // Fetch recent orders with correct structure
    const { data: recentOrdersData, error: recentError } = await supabase
      .from('orders')
      .select(`
        id,
        customer_session_id,
        total_amount,
        status,
        created_at
      `)
      .eq('restaurant_id', restaurantId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (recentError) {
      console.error('📊 Error fetching recent orders:', recentError);
      throw recentError;
    }

    console.log('📊 Recent orders raw data:', recentOrdersData);

    // Get order items count for each order
    const recentOrders: RecentOrder[] = [];
    if (recentOrdersData && recentOrdersData.length > 0) {
      for (const order of recentOrdersData) {
        // Get item count for this order
        const { data: orderItems, error: itemsError } = await supabase
          .from('order_items')
          .select('id')
          .eq('order_id', order.id);

        const itemCount = orderItems?.length || 0;

        recentOrders.push({
          id: order.id,
          customerName: order.customer_session_id ? `Customer ${order.customer_session_id.slice(-4)}` : 'Anonymous',
          totalAmount: parseFloat(order.total_amount),
          status: order.status,
          createdAt: order.created_at,
          itemCount
        });
      }
    }

    // Fetch hourly stats for today
    const todayDate = today.toISOString().split('T')[0];
    console.log('📊 Fetching hourly stats for date:', todayDate);

    const { data: hourlyData, error: hourlyError } = await supabase
      .from('menu_sales_analytics')
      .select('order_hour, order_count, total_sales')
      .eq('restaurant_id', restaurantId)
      .eq('order_date', todayDate)
      .order('order_hour');

    if (hourlyError) {
      console.error('📊 Error fetching hourly data:', hourlyError);
      throw hourlyError;
    }

    console.log('📊 Hourly data raw:', hourlyData);

    const hourlyStats: HourlyStats[] = [];
    for (let hour = 0; hour < 24; hour++) {
      const hourData = hourlyData?.filter(h => parseInt(h.order_hour) === hour) || [];
      const orderCount = hourData.reduce((sum, h) => sum + h.order_count, 0);
      const revenue = hourData.reduce((sum, h) => sum + parseFloat(h.total_sales), 0);
      
      hourlyStats.push({
        hour,
        orderCount,
        revenue
      });
    }

    const result = {
      todayOrders,
      todayRevenue,
      avgOrderValue,
      currentTraffic,
      trafficStatus,
      popularItems,
      recentOrders,
      hourlyStats
    };

    console.log('📊 Final dashboard metrics:', result);

    return result;

  } catch (error) {
    console.error('📊 Error fetching dashboard metrics:', error);
    throw error;
  }
}

/**
 * Fetch current traffic data using the proper traffic service
 */
export async function fetchCurrentTraffic(restaurantId: string): Promise<TrafficData | null> {
  try {
    // Use the traffic service to get comprehensive traffic data
    const trafficData = await getTrafficData(restaurantId);
    
    if (!trafficData) return null;

    return {
      currentReading: Math.round(trafficData.current_occupancy * 100),
      location: 'Restaurant',
      sensorType: 'combined',
      status: 'active',
      lastUpdated: trafficData.last_updated
    };
  } catch (error) {
    console.error('Error fetching traffic data:', error);
    return null;
  }
}

/**
 * Fetch weekly revenue trend
 */
export async function fetchWeeklyRevenue(restaurantId: string): Promise<{ date: string; revenue: number }[]> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select('total_amount, created_at')
      .eq('restaurant_id', restaurantId)
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
      .order('created_at');

    if (error) throw error;

    // Group by date
    const dailyRevenue: { [key: string]: number } = {};
    
    data?.forEach(order => {
      const date = order.created_at.split('T')[0];
      dailyRevenue[date] = (dailyRevenue[date] || 0) + parseFloat(order.total_amount);
    });

    // Convert to array format
    return Object.entries(dailyRevenue).map(([date, revenue]) => ({
      date,
      revenue
    }));

  } catch (error) {
    console.error('Error fetching weekly revenue:', error);
    return [];
  }
}

/**
 * Create a traffic record for testing dynamic pricing
 */
export async function createTrafficRecord(restaurantId: string, occupancyPercentage: number): Promise<boolean> {
  try {
    // Convert percentage to 0-1 range for the traffic service
    const occupancyValue = occupancyPercentage / 100;
    
    // First check if traffic sensor exists
    const { data: existingSensor } = await supabase
      .from('traffic_sensors')
      .select('id')
      .eq('restaurant_id', restaurantId)
      .limit(1);

    if (existingSensor && existingSensor.length > 0) {
      // Update existing sensor
      const { error } = await supabase
        .from('traffic_sensors')
        .update({
          latest_reading: occupancyValue,
          updated_at: new Date().toISOString()
        })
        .eq('restaurant_id', restaurantId);

      if (error) throw error;
    } else {
      // Create new sensor
      const { error } = await supabase
        .from('traffic_sensors')
        .insert({
          restaurant_id: restaurantId,
          location: 'Main Entrance',
          sensor_type: 'motion',
          latest_reading: occupancyValue,
          status: 'active'
        });

      if (error) throw error;
    }

    return true;
  } catch (error) {
    console.error('Error creating traffic record:', error);
    return false;
  }
} 