import { supabase } from '@/integrations/supabase/client';

export interface CustomerFeedbackData {
  restaurant_id: string;
  order_id: string;
  table_id?: string;
  customer_email?: string;
  food_rating?: number;
  service_rating?: number;
  app_rating?: number;
  comments?: string;
  sentiment_score?: number;
  sentiment_magnitude?: number;
}

export interface SyncResponse {
  success: boolean;
  message: string;
  feedback_id?: string;
  error?: string;
}

class FeedbackSyncService {
  private static readonly SME_ANALYTICA_API_URL = import.meta.env.VITE_API_BASE_URL || 'https://api.smeanalytica.dev';
  private static readonly SYNC_ENDPOINT = '/api/v1/feedback/restaurant-feedback';

  /**
   * Sync customer feedback to main SME Analytica sentiment analysis system
   */
  static async syncFeedbackToSentimentAnalysis(
    feedbackData: CustomerFeedbackData
  ): Promise<SyncResponse> {
    try {
      // First, store feedback in local ROS database
      const localResult = await this.storeFeedbackLocally(feedbackData);
      
      if (!localResult.success) {
        console.error('Failed to store feedback locally:', localResult.error);
        // Continue with sync attempt even if local storage fails
      }

      // Then sync to main SME Analytica system
      const syncResult = await this.syncToMainSystem(feedbackData);
      
      return syncResult;
    } catch (error) {
      console.error('Error in feedback sync process:', error);
      return {
        success: false,
        message: 'Failed to sync feedback',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Store feedback in local ROS database (customer_feedback table)
   */
  private static async storeFeedbackLocally(
    feedbackData: CustomerFeedbackData
  ): Promise<SyncResponse> {
    try {
      const { data, error } = await supabase
        .from('customer_feedback')
        .insert({
          order_id: feedbackData.order_id,
          restaurant_id: feedbackData.restaurant_id,
          table_id: feedbackData.table_id,
          food_rating: feedbackData.food_rating,
          service_rating: feedbackData.service_rating,
          app_rating: feedbackData.app_rating,
          comments: feedbackData.comments,
          customer_email: feedbackData.customer_email,
          sentiment_score: feedbackData.sentiment_score,
          sentiment_magnitude: feedbackData.sentiment_magnitude,
        })
        .select()
        .single();

      if (error) {
        return {
          success: false,
          message: 'Failed to store feedback locally',
          error: error.message
        };
      }

      return {
        success: true,
        message: 'Feedback stored locally successfully',
        feedback_id: data.id
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error storing feedback locally',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Sync feedback to main SME Analytica sentiment analysis system
   */
  private static async syncToMainSystem(
    feedbackData: CustomerFeedbackData
  ): Promise<SyncResponse> {
    try {
      // Get auth token for API request
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.access_token) {
        return {
          success: false,
          message: 'No authentication token available',
          error: 'Authentication required'
        };
      }

      const response = await fetch(
        `${this.SME_ANALYTICA_API_URL}${this.SYNC_ENDPOINT}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify(feedbackData)
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      return {
        success: true,
        message: 'Feedback synced to sentiment analysis successfully',
        feedback_id: result.id
      };
    } catch (error) {
      console.error('Error syncing to main system:', error);
      
      // Store sync failure for retry later
      await this.queueForRetry(feedbackData, error);
      
      return {
        success: false,
        message: 'Failed to sync to sentiment analysis system',
        error: error instanceof Error ? error.message : 'Network error'
      };
    }
  }

  /**
   * Queue failed syncs for retry later
   */
  private static async queueForRetry(
    feedbackData: CustomerFeedbackData,
    error: Error | unknown
  ): Promise<void> {
    try {
      // Store failed sync attempts in a queue table for retry
      await supabase.from('sync_queue').insert({
        type: 'feedback_sync',
        data: feedbackData,
        error_message: error instanceof Error ? error.message : 'Unknown error',
        retry_count: 0,
        status: 'pending'
      });
    } catch (queueError) {
      console.error('Failed to queue sync for retry:', queueError);
    }
  }

  /**
   * Retry failed syncs
   */
  static async retryFailedSyncs(): Promise<void> {
    try {
      const { data: failedSyncs, error } = await supabase
        .from('sync_queue')
        .select('*')
        .eq('type', 'feedback_sync')
        .eq('status', 'pending')
        .lt('retry_count', 3)
        .limit(10);

      if (error || !failedSyncs) return;

      for (const sync of failedSyncs) {
        const result = await this.syncToMainSystem(sync.data);
        
        if (result.success) {
          // Mark as completed
          await supabase
            .from('sync_queue')
            .update({ status: 'completed' })
            .eq('id', sync.id);
        } else {
          // Increment retry count
          await supabase
            .from('sync_queue')
            .update({ 
              retry_count: sync.retry_count + 1,
              status: sync.retry_count >= 2 ? 'failed' : 'pending'
            })
            .eq('id', sync.id);
        }
      }
    } catch (error) {
      console.error('Error retrying failed syncs:', error);
    }
  }

  /**
   * Calculate sentiment score from ratings
   */
  static calculateSentimentScore(
    foodRating?: number,
    serviceRating?: number,
    appRating?: number
  ): { score: number; magnitude: number } {
    const ratings = [foodRating, serviceRating, appRating].filter(r => r !== undefined && r > 0);
    
    if (ratings.length === 0) {
      return { score: 0.5, magnitude: 0.1 };
    }

    // Convert 1-5 scale to 0-1 sentiment score
    const avgRating = ratings.reduce((sum, rating) => sum + rating!, 0) / ratings.length;
    const score = (avgRating - 1) / 4; // Convert to 0-1 scale
    
    // Magnitude based on number of ratings and spread
    const magnitude = Math.min(0.9, 0.3 + (ratings.length / 3) * 0.6);
    
    return { score, magnitude };
  }
}

export default FeedbackSyncService; 