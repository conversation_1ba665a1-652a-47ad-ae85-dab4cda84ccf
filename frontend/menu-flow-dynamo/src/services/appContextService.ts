/**
 * App Context Service for Restaurant Management System
 * Handles application identification, session management, and analytics
 */

import { supabase } from '@/integrations/supabase/client';
import { appConfig, createAppContext, type AppContext } from '@/config/app';

export interface AppSession {
  id: string;
  userId: string;
  applicationName: string;
  applicationId: string;
  sessionId: string;
  deviceId?: string;
  sessionStart: string;
  sessionEnd?: string;
  sessionData: Record<string, any>;
}

export interface AppEvent {
  id: string;
  applicationName: string;
  eventName: string;
  eventType: string;
  eventData: Record<string, any>;
  screenName?: string;
  featureName?: string;
  recordedAt: string;
}

export interface AppAnalytics {
  metricName: string;
  metricValue: number;
  metricType?: 'counter' | 'gauge' | 'histogram';
  dimensions?: Record<string, any>;
  tags?: Record<string, any>;
}

class AppContextService {
  private currentSession: AppSession | null = null;
  private appContext: AppContext | null = null;

  /**
   * Initialize app context for the current user and restaurant
   */
  async initializeAppContext(userId: string, restaurantId?: string): Promise<AppContext> {
    this.appContext = createAppContext(userId, restaurantId);
    
    // Start a new app session
    await this.startAppSession(userId, { restaurantId });
    
    return this.appContext;
  }

  /**
   * Start a new app session
   */
  async startAppSession(userId: string, sessionData: Record<string, any> = {}): Promise<string> {
    try {
      if (!this.appContext) {
        this.appContext = createAppContext(userId, sessionData.restaurantId);
      }

      const { data, error } = await supabase.rpc('start_app_session', {
        p_user_id: userId,
        p_application_name: appConfig.applicationName,
        p_application_id: appConfig.applicationId,
        p_session_id: this.appContext.sessionId,
        p_session_data: {
          ...sessionData,
          restaurantId: this.appContext.restaurantId,
          features: appConfig.FEATURE_FLAGS,
        },
      });

      if (error) {
        console.error('Error starting app session:', error);
        throw error;
      }

      console.log('Restaurant app session started:', data);
      return data;
    } catch (error) {
      console.error('Failed to start app session:', error);
      throw error;
    }
  }

  /**
   * End the current app session
   */
  async endAppSession(): Promise<boolean> {
    try {
      if (!this.appContext) {
        return false;
      }

      const { data, error } = await supabase.rpc('end_app_session', {
        p_session_id: this.appContext.sessionId,
      });

      if (error) {
        console.error('Error ending app session:', error);
        throw error;
      }

      this.currentSession = null;
      return data;
    } catch (error) {
      console.error('Failed to end app session:', error);
      return false;
    }
  }

  /**
   * Track restaurant app event
   */
  async trackEvent(
    eventName: string,
    eventData: Record<string, any> = {},
    eventType: string = 'user_action',
    screenName?: string,
    featureName?: string
  ): Promise<string | null> {
    try {
      const enhancedEventData = {
        ...eventData,
        restaurantId: this.appContext?.restaurantId,
        applicationFeatures: appConfig.FEATURE_FLAGS,
      };

      const { data, error } = await supabase.rpc('track_app_event', {
        p_application_name: appConfig.applicationName,
        p_event_name: eventName,
        p_user_id: this.appContext?.userId || null,
        p_application_id: appConfig.applicationId,
        p_session_id: this.appContext?.sessionId || null,
        p_event_type: eventType,
        p_event_data: enhancedEventData,
        p_screen_name: screenName,
        p_feature_name: featureName,
      });

      if (error) {
        console.error('Error tracking restaurant app event:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Failed to track restaurant app event:', error);
      return null;
    }
  }

  /**
   * Record restaurant analytics metrics
   */
  async recordAnalytics(analytics: AppAnalytics): Promise<boolean> {
    try {
      const { error } = await supabase.from('app_analytics').insert([
        {
          application_name: appConfig.applicationName,
          application_id: appConfig.applicationId,
          user_id: this.appContext?.userId || null,
          metric_name: analytics.metricName,
          metric_value: analytics.metricValue,
          metric_type: analytics.metricType || 'counter',
          dimensions: {
            ...analytics.dimensions,
            restaurantId: this.appContext?.restaurantId,
          },
          tags: {
            ...analytics.tags,
            application: 'restaurant_management',
          },
        },
      ]);

      if (error) {
        console.error('Error recording restaurant analytics:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Failed to record restaurant analytics:', error);
      return false;
    }
  }

  /**
   * Track restaurant-specific events
   */
  async trackRestaurantEvent(
    eventName: string,
    restaurantId: string,
    eventData: Record<string, any> = {}
  ): Promise<string | null> {
    return this.trackEvent(
      eventName,
      { ...eventData, restaurantId },
      'restaurant_operation'
    );
  }

  /**
   * Track order events
   */
  async trackOrderEvent(
    eventName: string,
    orderId: string,
    restaurantId: string,
    eventData: Record<string, any> = {}
  ): Promise<string | null> {
    return this.trackEvent(
      eventName,
      { ...eventData, orderId, restaurantId },
      'order_management',
      undefined,
      'ordering'
    );
  }

  /**
   * Track dynamic pricing events
   */
  async trackDynamicPricingEvent(
    eventName: string,
    restaurantId: string,
    eventData: Record<string, any> = {}
  ): Promise<string | null> {
    return this.trackEvent(
      eventName,
      { ...eventData, restaurantId },
      'dynamic_pricing',
      undefined,
      'pricing'
    );
  }

  /**
   * Get restaurant preferences
   */
  async getRestaurantPreferences(): Promise<Record<string, any>> {
    try {
      if (!this.appContext?.userId) {
        return {};
      }

      const { data, error } = await supabase
        .from('user_app_preferences')
        .select('preferences')
        .eq('user_id', this.appContext.userId)
        .eq('application_name', appConfig.applicationName)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error getting restaurant preferences:', error);
        throw error;
      }

      return data?.preferences || {};
    } catch (error) {
      console.error('Failed to get restaurant preferences:', error);
      return {};
    }
  }

  /**
   * Save restaurant preferences
   */
  async saveRestaurantPreferences(preferences: Record<string, any>): Promise<boolean> {
    try {
      if (!this.appContext?.userId) {
        return false;
      }

      const { error } = await supabase.from('user_app_preferences').upsert([
        {
          user_id: this.appContext.userId,
          application_name: appConfig.applicationName,
          application_id: appConfig.applicationId,
          preferences: {
            ...preferences,
            restaurantId: this.appContext.restaurantId,
          },
        },
      ]);

      if (error) {
        console.error('Error saving restaurant preferences:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Failed to save restaurant preferences:', error);
      return false;
    }
  }

  /**
   * Get current app context
   */
  getAppContext(): AppContext | null {
    return this.appContext;
  }

  /**
   * Check if app context is initialized
   */
  isInitialized(): boolean {
    return this.appContext !== null;
  }

  /**
   * Get application configuration
   */
  getAppConfig() {
    return appConfig;
  }

  /**
   * Update restaurant context
   */
  updateRestaurantContext(restaurantId: string): void {
    if (this.appContext) {
      this.appContext.restaurantId = restaurantId;
    }
  }
}

export const appContextService = new AppContextService();
export default appContextService;