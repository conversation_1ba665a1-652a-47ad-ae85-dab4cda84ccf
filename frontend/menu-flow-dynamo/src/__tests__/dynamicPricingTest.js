// Dynamic Pricing System Test
// This test is designed to be run from the browser console
// No imports needed - the functions are accessed from the window object

// Test restaurant ID (La Pepica Restaurant)
const TEST_RESTAURANT_ID = '1fd5e254-ab0e-468b-adfe-22217536eee6';

/**
 * Test the dynamic pricing system end-to-end
 */
async function testDynamicPricing() {
  console.log('🧪 STARTING DYNAMIC PRICING TEST');
  console.log('--------------------------------');
  
  try {
    // Step 1: Check if dynamic pricing is enabled for the restaurant
    const isPricingEnabled = await isDynamicPricingEnabled(TEST_RESTAURANT_ID);
    console.log(`1️⃣ Dynamic pricing enabled: ${isPricingEnabled}`);
    
    if (!isPricingEnabled) {
      console.error('❌ Test failed: Dynamic pricing is not enabled for test restaurant');
      return false;
    }
    
    // Step 2: Get the restaurant's pricing rules
    const pricingRules = await getPricingRules(TEST_RESTAURANT_ID, null, true);
    console.log('2️⃣ Pricing rules:', JSON.stringify(pricingRules, null, 2));
    
    if (!pricingRules.highTraffic || !pricingRules.mediumTraffic || !pricingRules.lowTraffic) {
      console.error('❌ Test failed: Pricing rules are incomplete');
      return false;
    }
    
    // Step 3: Get current traffic level
    const trafficLevel = await getCurrentTraffic(TEST_RESTAURANT_ID);
    console.log(`3️⃣ Current traffic level: ${(trafficLevel * 100).toFixed(1)}%`);
    
    // Step 4: Get menu items to apply pricing to
    const { data: menuItems, error: menuError } = await supabase
      .from('menu_items')
      .select('*')
      .eq('restaurant_id', TEST_RESTAURANT_ID)
      .limit(10);
      
    if (menuError) {
      console.error('❌ Test failed: Could not fetch menu items', menuError);
      return false;
    }
    
    console.log(`4️⃣ Found ${menuItems.length} menu items`);
    
    // Log a few items before dynamic pricing
    console.log('\nBEFORE DYNAMIC PRICING:');
    console.table(
      menuItems.slice(0, 3).map(item => ({
        name: item.name,
        category: item.category,
        base_price: item.base_price,
        current_price: item.current_price,
        factor: item.price_adjustment_factor
      }))
    );
    
    // Step 5: Apply dynamic pricing to menu items
    const updatedItems = await applyDynamicPricing(menuItems, TEST_RESTAURANT_ID, false);
    
    if (!updatedItems || updatedItems.length === 0) {
      console.error('❌ Test failed: Dynamic pricing did not return updated items');
      return false;
    }
    
    // Log the same items after dynamic pricing
    console.log('\nAFTER DYNAMIC PRICING:');
    console.table(
      updatedItems.slice(0, 3).map(item => ({
        name: item.name,
        category: item.category,
        base_price: item.base_price,
        current_price: item.current_price,
        factor: item.price_adjustment_factor
      }))
    );
    
    // Step 6: Verify that price adjustments were applied correctly
    let hasAdjustments = false;
    
    for (const item of updatedItems) {
      // If any item has a price adjustment factor different from 1.0, pricing was applied
      if (item.price_adjustment_factor !== 1.0) {
        hasAdjustments = true;
        break;
      }
    }
    
    if (!hasAdjustments) {
      console.warn('⚠️ No price adjustments were applied, prices remained the same');
    } else {
      console.log('✅ Price adjustments were successfully applied');
    }
    
    // Step 7: Compare with expected price adjustments based on traffic
    const expectedFactor = calculateExpectedFactor(trafficLevel, pricingRules);
    console.log(`7️⃣ Expected price factor based on traffic (${(trafficLevel * 100).toFixed(1)}%): ${expectedFactor.toFixed(2)}`);
    
    // Step 8: Verify if items are saved to database with new prices
    const saveResult = await saveUpdatedPrices(updatedItems);
    console.log(`8️⃣ Saved ${saveResult.savedCount} items to database`);
    
    console.log('\n--------------------------------');
    console.log('✅ DYNAMIC PRICING TEST COMPLETED SUCCESSFULLY');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
}

/**
 * Calculate the expected price factor based on traffic level and pricing rules
 */
function calculateExpectedFactor(trafficLevel, pricingRules) {
  let priceFactor = 1.0;
  
  // Apply rules based on traffic thresholds
  if (trafficLevel >= (pricingRules?.highTraffic?.threshold || 0.8)) {
    // High traffic
    const percentage = Math.abs(pricingRules?.highTraffic?.percentage || 10);
    priceFactor = 1 + (percentage / 100);
  } else if (trafficLevel >= (pricingRules?.mediumTraffic?.threshold || 0.5)) {
    // Medium traffic
    const percentage = Math.abs(pricingRules?.mediumTraffic?.percentage || 5);
    priceFactor = 1 + (percentage / 100);
  } else if (trafficLevel <= (pricingRules?.lowTraffic?.threshold || 0.2)) {
    // Low traffic
    const percentage = Math.abs(pricingRules?.lowTraffic?.percentage || 5);
    
    // Check if we should decrease (negative percentage) or increase prices
    if ((pricingRules?.lowTraffic?.percentage || 0) < 0) {
      priceFactor = 1 - (percentage / 100); // Discount
    } else {
      priceFactor = 1 + (percentage / 100); // Increase
    }
  }
  
  return priceFactor;
}

/**
 * Save updated prices to the database
 */
async function saveUpdatedPrices(menuItems) {
  let savedCount = 0;
  
  for (const item of menuItems) {
    try {
      const { error } = await supabase
        .from('menu_items')
        .update({
          current_price: item.current_price,
          price_adjustment_factor: item.price_adjustment_factor
        })
        .eq('id', item.id);
        
      if (!error) {
        savedCount++;
      }
    } catch (e) {
      console.error(`Error saving item ${item.id}:`, e);
    }
  }
  
  return { savedCount };
}

// Export the test function so it can be run from the command line
export default testDynamicPricing;

// If this script is run directly, execute the test
if (typeof window !== 'undefined') {
  window.testDynamicPricing = testDynamicPricing;
  console.log('Run dynamic pricing test using: testDynamicPricing()');
}
