// Dynamic Pricing Disabled Test
// This test verifies that when dynamic pricing is disabled by the restaurant owner,
// the customer-facing menu shows original prices (not dynamic prices)

// Test restaurant ID (La Pepica Restaurant)
const TEST_RESTAURANT_ID = '1fd5e254-ab0e-468b-adfe-22217536eee6';

/**
 * Test that dynamic pricing respects the disabled state
 */
async function testDynamicPricingDisabled() {
  console.log('🧪 TESTING DYNAMIC PRICING DISABLED STATE');
  console.log('==========================================');
  
  try {
    // Step 1: Disable dynamic pricing for the restaurant
    console.log('1️⃣ Disabling dynamic pricing...');
    const disableResult = await setDynamicPricingEnabled(false, TEST_RESTAURANT_ID);
    
    if (!disableResult) {
      console.error('❌ Failed to disable dynamic pricing');
      return false;
    }
    
    console.log('✅ Dynamic pricing disabled successfully');
    
    // Step 2: Verify that isDynamicPricingEnabled returns false
    console.log('2️⃣ Checking if dynamic pricing is disabled...');
    const isPricingEnabled = await isDynamicPricingEnabled(TEST_RESTAURANT_ID);
    
    if (isPricingEnabled) {
      console.error('❌ Test failed: isDynamicPricingEnabled still returns true after disabling');
      return false;
    }
    
    console.log('✅ isDynamicPricingEnabled correctly returns false');
    
    // Step 3: Get menu items for testing
    console.log('3️⃣ Fetching menu items...');
    const { data: menuItems, error: menuError } = await supabase
      .from('menu_items')
      .select('*')
      .eq('restaurant_id', TEST_RESTAURANT_ID)
      .limit(5);
      
    if (menuError || !menuItems?.length) {
      console.error('❌ Failed to fetch menu items:', menuError);
      return false;
    }
    
    console.log(`✅ Found ${menuItems.length} menu items`);
    
    // Step 4: Apply dynamic pricing (should return original items unchanged)
    console.log('4️⃣ Testing applyDynamicPricing with disabled state...');
    
    // Test both admin and customer views
    const adminViewItems = await applyDynamicPricing([...menuItems], TEST_RESTAURANT_ID, false);
    const customerViewItems = await applyDynamicPricing([...menuItems], TEST_RESTAURANT_ID, true);
    
    // Step 5: Verify that prices remain unchanged
    console.log('5️⃣ Verifying prices remain unchanged...');
    
    let allPricesUnchanged = true;
    const priceComparison = [];
    
    for (let i = 0; i < menuItems.length; i++) {
      const original = menuItems[i];
      const adminResult = adminViewItems[i];
      const customerResult = customerViewItems[i];
      
      // Check if prices were modified
      const adminPriceChanged = adminResult.price !== original.base_price;
      const customerPriceChanged = customerResult.price !== original.base_price;
      
      if (adminPriceChanged || customerPriceChanged) {
        allPricesUnchanged = false;
      }
      
      priceComparison.push({
        name: original.name,
        originalPrice: original.base_price,
        adminPrice: adminResult.price,
        customerPrice: customerResult.price,
        adminChanged: adminPriceChanged,
        customerChanged: customerPriceChanged,
        isDynamicallyPriced: customerResult.isDynamicallyPriced || false
      });
    }
    
    // Display comparison table
    console.log('\nPRICE COMPARISON (Dynamic Pricing DISABLED):');
    console.table(priceComparison.map(item => ({
      item: item.name,
      original: `$${item.originalPrice.toFixed(2)}`,
      admin: `$${item.adminPrice.toFixed(2)}`,
      customer: `$${item.customerPrice.toFixed(2)}`,
      adminChanged: item.adminChanged ? '❌' : '✅',
      customerChanged: item.customerChanged ? '❌' : '✅',
      dynamicallyPriced: item.isDynamicallyPriced ? '❌' : '✅'
    })));
    
    if (!allPricesUnchanged) {
      console.error('❌ Test failed: Some prices were modified despite dynamic pricing being disabled');
      return false;
    }
    
    console.log('✅ All prices remained unchanged as expected');
    
    // Step 6: Test with high traffic to ensure pricing is still disabled
    console.log('6️⃣ Testing with high traffic (should still be disabled)...');
    
    // Create high traffic record
    await createTrafficRecord(TEST_RESTAURANT_ID, 0.9); // 90% traffic
    
    // Apply pricing again
    const highTrafficItems = await applyDynamicPricing([...menuItems], TEST_RESTAURANT_ID, true);
    
    // Verify prices are still unchanged
    let stillUnchanged = true;
    for (let i = 0; i < menuItems.length; i++) {
      if (highTrafficItems[i].price !== menuItems[i].base_price) {
        stillUnchanged = false;
        break;
      }
    }
    
    if (!stillUnchanged) {
      console.error('❌ Test failed: High traffic caused price changes despite dynamic pricing being disabled');
      return false;
    }
    
    console.log('✅ Prices remained unchanged even with high traffic');
    
    console.log('\n==========================================');
    console.log('✅ DYNAMIC PRICING DISABLED TEST PASSED');
    console.log('   The bug has been fixed! 🎉');
    console.log('   Customer menus now respect the disabled state.');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
}

/**
 * Test the complete flow: disable -> enable -> disable
 */
async function testDynamicPricingToggle() {
  console.log('\n🔄 TESTING DYNAMIC PRICING TOGGLE FLOW');
  console.log('======================================');
  
  try {
    // Get menu items for testing
    const { data: menuItems, error: menuError } = await supabase
      .from('menu_items')
      .select('*')
      .eq('restaurant_id', TEST_RESTAURANT_ID)
      .limit(3);
      
    if (menuError || !menuItems?.length) {
      console.error('❌ Failed to fetch menu items:', menuError);
      return false;
    }
    
    // Test 1: Disable pricing
    console.log('1️⃣ Testing DISABLED state...');
    await setDynamicPricingEnabled(false, TEST_RESTAURANT_ID);
    
    const disabledItems = await applyDynamicPricing([...menuItems], TEST_RESTAURANT_ID, true);
    const disabledUnchanged = disabledItems.every((item, i) => item.price === menuItems[i].base_price);
    
    console.log(`   Disabled state: ${disabledUnchanged ? '✅ Prices unchanged' : '❌ Prices changed'}`);
    
    // Test 2: Enable pricing
    console.log('2️⃣ Testing ENABLED state...');
    await setDynamicPricingEnabled(true, TEST_RESTAURANT_ID);
    
    // Create high traffic to ensure pricing is applied
    await createTrafficRecord(TEST_RESTAURANT_ID, 0.85);
    
    const enabledItems = await applyDynamicPricing([...menuItems], TEST_RESTAURANT_ID, true);
    const enabledChanged = enabledItems.some((item, i) => item.price !== menuItems[i].base_price);
    
    console.log(`   Enabled state: ${enabledChanged ? '✅ Prices changed' : '❌ Prices unchanged'}`);
    
    // Test 3: Disable again
    console.log('3️⃣ Testing DISABLED state again...');
    await setDynamicPricingEnabled(false, TEST_RESTAURANT_ID);
    
    const disabledAgainItems = await applyDynamicPricing([...menuItems], TEST_RESTAURANT_ID, true);
    const disabledAgainUnchanged = disabledAgainItems.every((item, i) => item.price === menuItems[i].base_price);
    
    console.log(`   Disabled again: ${disabledAgainUnchanged ? '✅ Prices unchanged' : '❌ Prices changed'}`);
    
    const allTestsPassed = disabledUnchanged && enabledChanged && disabledAgainUnchanged;
    
    console.log('\n======================================');
    console.log(`${allTestsPassed ? '✅' : '❌'} TOGGLE TEST ${allTestsPassed ? 'PASSED' : 'FAILED'}`);
    
    return allTestsPassed;
    
  } catch (error) {
    console.error('❌ Toggle test failed with error:', error);
    return false;
  }
}

/**
 * Helper function to create a traffic record
 */
async function createTrafficRecord(restaurantId, trafficLevel) {
  try {
    const { error } = await supabase
      .from('traffic_data')
      .insert({
        restaurant_id: restaurantId,
        traffic_level: trafficLevel,
        timestamp: new Date().toISOString()
      });
      
    if (error) {
      console.warn('Could not create traffic record:', error);
    }
  } catch (e) {
    console.warn('Error creating traffic record:', e);
  }
}

/**
 * Run all tests
 */
async function runAllDisabledTests() {
  console.log('🚀 RUNNING ALL DYNAMIC PRICING DISABLED TESTS');
  console.log('==============================================\n');
  
  const test1 = await testDynamicPricingDisabled();
  const test2 = await testDynamicPricingToggle();
  
  console.log('\n==============================================');
  console.log('📊 TEST RESULTS SUMMARY:');
  console.log(`   Disabled State Test: ${test1 ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`   Toggle Flow Test: ${test2 ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`   Overall Result: ${test1 && test2 ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (test1 && test2) {
    console.log('\n🎉 The dynamic pricing disabled bug has been successfully fixed!');
    console.log('   Restaurant owners can now disable dynamic pricing and customers');
    console.log('   will see the original menu prices as expected.');
  }
  
  return test1 && test2;
}

// Export functions for browser console use
if (typeof window !== 'undefined') {
  window.testDynamicPricingDisabled = testDynamicPricingDisabled;
  window.testDynamicPricingToggle = testDynamicPricingToggle;
  window.runAllDisabledTests = runAllDisabledTests;
  
  console.log('🧪 Dynamic Pricing Disabled Tests Available:');
  console.log('   - testDynamicPricingDisabled()');
  console.log('   - testDynamicPricingToggle()');
  console.log('   - runAllDisabledTests()');
}

export { testDynamicPricingDisabled, testDynamicPricingToggle, runAllDisabledTests }; 