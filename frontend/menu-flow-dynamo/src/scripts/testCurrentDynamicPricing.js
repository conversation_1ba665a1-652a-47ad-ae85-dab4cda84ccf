/**
 * Test Current Dynamic Pricing Implementation
 * 
 * This script tests the current dynamic pricing with real traffic data
 * Run this in the browser console on the Menu.tsx page
 */

console.log('🧪 TESTING CURRENT DYNAMIC PRICING');
console.log('==================================');

async function testCurrentDynamicPricing() {
  try {
    // Test restaurant: My Abrast (has 40% occupancy, should trigger medium traffic pricing)
    const restaurantId = '01aaadc0-bf00-47bf-b642-5c1fa8a3b912';
    
    console.log('1️⃣ Testing traffic calculation...');
    
    // Import the traffic service (adjust path as needed)
    const { getCurrentTraffic } = await import('/src/services/trafficService.ts');
    const currentTraffic = await getCurrentTraffic(restaurantId);
    
    console.log(`📊 Current traffic: ${Math.round(currentTraffic * 100)}%`);
    
    console.log('2️⃣ Testing dynamic pricing enabled check...');
    
    // Import the dynamic pricing service
    const { isDynamicPricingEnabled } = await import('/src/services/dynamicPricingService.ts');
    const isPricingEnabled = await isDynamicPricingEnabled(restaurantId);
    
    console.log(`⚙️ Dynamic pricing enabled: ${isPricingEnabled}`);
    
    console.log('3️⃣ Testing menu items with dynamic pricing...');
    
    // Test menu items
    const testMenuItems = [
      {
        id: '160c1f2c-0c6d-4be4-a01a-bc68068dd48d',
        name: 'OLHOPS',
        category: 'drinks',
        base_price: 3.50,
        current_price: 3.50,
        price_adjustment_factor: 1.00,
        restaurant_id: restaurantId
      }
    ];
    
    // Import and apply dynamic pricing
    const { applyDynamicPricing } = await import('/src/services/dynamicPricingService.ts');
    const adjustedItems = await applyDynamicPricing(testMenuItems, restaurantId);
    
    console.log('📋 Original vs Adjusted Prices:');
    testMenuItems.forEach((original, index) => {
      const adjusted = adjustedItems[index];
      console.log(`${original.name} (${original.category}):`);
      console.log(`  Original: $${original.base_price}`);
      console.log(`  Adjusted: $${adjusted.current_price}`);
      console.log(`  Factor: ${adjusted.price_adjustment_factor}`);
      console.log(`  Change: ${((adjusted.current_price - original.base_price) / original.base_price * 100).toFixed(1)}%`);
    });
    
    console.log('4️⃣ Expected vs Actual:');
    console.log('With 40% occupancy and medium traffic rules (+5%), OLHOPS should be:');
    console.log(`Expected: $${(3.50 * 1.05).toFixed(2)}`);
    console.log(`Actual: $${adjustedItems[0].current_price}`);
    
    if (adjustedItems[0].current_price === 3.50) {
      console.log('❌ ISSUE: Price not adjusted! Dynamic pricing not working.');
    } else {
      console.log('✅ SUCCESS: Dynamic pricing is working!');
    }
    
  } catch (error) {
    console.error('❌ Error testing dynamic pricing:', error);
  }
}

// Auto-run the test
testCurrentDynamicPricing();

// Also export for manual testing
window.testCurrentDynamicPricing = testCurrentDynamicPricing;

console.log('💡 You can also run: testCurrentDynamicPricing()'); 