#!/usr/bin/env node

/**
 * Test Runner for Dynamic Pricing Disabled Fix
 * 
 * This script verifies that the dynamic pricing bug has been fixed.
 * The bug was: customer menus still showed dynamic prices even when
 * the restaurant owner disabled dynamic pricing in the admin dashboard.
 * 
 * Usage:
 * - In browser console: Copy and paste this script, then run testDynamicPricingFix()
 * - Or include this script in your test page and call the functions
 */

console.log('🔧 DYNAMIC PRICING FIX VERIFICATION SCRIPT');
console.log('==========================================');
console.log('This script tests that the dynamic pricing disabled bug has been fixed.');
console.log('');

/**
 * Quick verification test that can be run in any environment
 */
async function testDynamicPricingFix() {
  console.log('🧪 RUNNING DYNAMIC PRICING FIX VERIFICATION');
  console.log('============================================');
  
  try {
    // Check if required functions are available
    if (typeof isDynamicPricingEnabled === 'undefined') {
      console.error('❌ isDynamicPricingEnabled function not available');
      console.log('💡 Make sure you are running this in a page where the dynamic pricing service is loaded');
      return false;
    }
    
    if (typeof applyDynamicPricing === 'undefined') {
      console.error('❌ applyDynamicPricing function not available');
      console.log('💡 Make sure you are running this in a page where the dynamic pricing service is loaded');
      return false;
    }
    
    console.log('✅ Required functions are available');
    
    // Test the core fix: isDynamicPricingEnabled should default to false
    console.log('\n1️⃣ Testing isDynamicPricingEnabled fallback behavior...');
    
    // Test with a non-existent restaurant ID to trigger fallback logic
    const nonExistentRestaurantId = 'test-fallback-' + Date.now();
    const fallbackResult = await isDynamicPricingEnabled(nonExistentRestaurantId);
    
    if (fallbackResult === false) {
      console.log('✅ isDynamicPricingEnabled correctly defaults to FALSE for unknown restaurants');
      console.log('   This means the bug is fixed - customer menus will show original prices');
      console.log('   when dynamic pricing data is not found or fails to load.');
    } else {
      console.error('❌ isDynamicPricingEnabled still defaults to TRUE for unknown restaurants');
      console.error('   This means the bug is NOT fixed - customers might still see dynamic prices');
      console.error('   even when the restaurant owner has disabled the feature.');
      return false;
    }
    
    // Test applyDynamicPricing with disabled state
    console.log('\n2️⃣ Testing applyDynamicPricing with disabled state...');
    
    // Create mock menu items
    const mockMenuItems = [
      { id: '1', name: 'Test Burger', price: 10.00, base_price: 10.00, category: 'food' },
      { id: '2', name: 'Test Drink', price: 5.00, base_price: 5.00, category: 'drinks' }
    ];
    
    // Mock the isDynamicPricingEnabled function to return false
    const originalFunction = window.isDynamicPricingEnabled;
    window.isDynamicPricingEnabled = async () => false;
    
    try {
      const result = await applyDynamicPricing(mockMenuItems, nonExistentRestaurantId, true);
      
      // Check if prices remained unchanged
      const pricesUnchanged = result.every((item, index) => 
        item.price === mockMenuItems[index].price
      );
      
      if (pricesUnchanged) {
        console.log('✅ applyDynamicPricing correctly returns unchanged prices when disabled');
      } else {
        console.error('❌ applyDynamicPricing modified prices even when disabled');
        return false;
      }
    } finally {
      // Restore original function
      window.isDynamicPricingEnabled = originalFunction;
    }
    
    console.log('\n============================================');
    console.log('🎉 DYNAMIC PRICING FIX VERIFICATION PASSED!');
    console.log('');
    console.log('✅ The bug has been successfully fixed:');
    console.log('   • isDynamicPricingEnabled defaults to FALSE when data is not found');
    console.log('   • applyDynamicPricing respects the disabled state');
    console.log('   • Customer menus will show original prices when dynamic pricing is disabled');
    console.log('');
    console.log('🏪 Restaurant owners can now confidently disable dynamic pricing');
    console.log('   knowing that customers will see the intended menu prices.');
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.log('\n💡 Troubleshooting:');
    console.log('   • Make sure you are on a page with the dynamic pricing service loaded');
    console.log('   • Check the browser console for any other errors');
    console.log('   • Verify that Supabase is properly configured');
    return false;
  }
}

/**
 * Detailed test that requires a real restaurant ID
 */
async function testWithRealRestaurant(restaurantId) {
  if (!restaurantId) {
    console.error('❌ Please provide a restaurant ID for detailed testing');
    return false;
  }
  
  console.log(`\n🏪 TESTING WITH REAL RESTAURANT: ${restaurantId}`);
  console.log('================================================');
  
  try {
    // Test 1: Check current state
    const currentState = await isDynamicPricingEnabled(restaurantId);
    console.log(`Current dynamic pricing state: ${currentState ? 'ENABLED' : 'DISABLED'}`);
    
    // Test 2: Disable and verify
    console.log('\n1️⃣ Disabling dynamic pricing...');
    const disableResult = await setDynamicPricingEnabled(false, restaurantId);
    
    if (!disableResult) {
      console.error('❌ Failed to disable dynamic pricing');
      return false;
    }
    
    const disabledState = await isDynamicPricingEnabled(restaurantId);
    if (disabledState) {
      console.error('❌ Dynamic pricing is still enabled after disabling');
      return false;
    }
    
    console.log('✅ Dynamic pricing successfully disabled');
    
    // Test 3: Verify customer menu shows original prices
    console.log('\n2️⃣ Testing customer menu with disabled pricing...');
    
    // Get real menu items
    const { data: menuItems, error } = await supabase
      .from('menu_items')
      .select('*')
      .eq('restaurant_id', restaurantId)
      .limit(3);
      
    if (error || !menuItems?.length) {
      console.warn('⚠️ Could not fetch menu items for detailed testing');
      return true; // Still consider the basic test passed
    }
    
    const customerMenuItems = await applyDynamicPricing(menuItems, restaurantId, true);
    
    // Verify prices are unchanged
    const pricesUnchanged = customerMenuItems.every((item, index) => 
      item.price === menuItems[index].base_price
    );
    
    if (pricesUnchanged) {
      console.log('✅ Customer menu shows original prices when dynamic pricing is disabled');
    } else {
      console.error('❌ Customer menu still shows modified prices despite being disabled');
      return false;
    }
    
    // Restore original state
    console.log('\n3️⃣ Restoring original state...');
    await setDynamicPricingEnabled(currentState, restaurantId);
    
    console.log('\n================================================');
    console.log('✅ DETAILED TEST PASSED!');
    
    return true;
    
  } catch (error) {
    console.error('❌ Detailed test failed:', error);
    return false;
  }
}

/**
 * Instructions for manual testing
 */
function showManualTestInstructions() {
  console.log('\n📋 MANUAL TESTING INSTRUCTIONS');
  console.log('==============================');
  console.log('');
  console.log('To manually verify the fix:');
  console.log('');
  console.log('1️⃣ Go to the Admin Dashboard');
  console.log('   • Navigate to the Dynamic Pricing section');
  console.log('   • Turn OFF the dynamic pricing toggle');
  console.log('   • Save the settings');
  console.log('');
  console.log('2️⃣ Open the Customer Menu');
  console.log('   • Go to the customer-facing menu page');
  console.log('   • Check that all prices match the base prices');
  console.log('   • Prices should NOT be dynamically adjusted');
  console.log('');
  console.log('3️⃣ Verify with High Traffic');
  console.log('   • Even during high traffic periods');
  console.log('   • Prices should remain at base levels');
  console.log('   • No dynamic adjustments should occur');
  console.log('');
  console.log('4️⃣ Test Re-enabling');
  console.log('   • Go back to Admin Dashboard');
  console.log('   • Turn ON the dynamic pricing toggle');
  console.log('   • Customer menu should now show dynamic prices');
  console.log('');
  console.log('✅ If all steps work as described, the bug is fixed!');
}

// Make functions available globally for browser console use
if (typeof window !== 'undefined') {
  window.testDynamicPricingFix = testDynamicPricingFix;
  window.testWithRealRestaurant = testWithRealRestaurant;
  window.showManualTestInstructions = showManualTestInstructions;
  
  console.log('\n🛠️ Available Test Functions:');
  console.log('   • testDynamicPricingFix() - Quick verification test');
  console.log('   • testWithRealRestaurant(restaurantId) - Detailed test with real data');
  console.log('   • showManualTestInstructions() - Manual testing guide');
  console.log('');
  console.log('💡 Start with: testDynamicPricingFix()');
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testDynamicPricingFix,
    testWithRealRestaurant,
    showManualTestInstructions
  };
} 