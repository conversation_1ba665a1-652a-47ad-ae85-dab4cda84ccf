/**
 * Debug Dynamic Pricing Flow
 * 
 * This script tests the entire dynamic pricing flow step by step
 * Run this in the browser console on any page
 */

console.log('🔧 DEBUGGING DYNAMIC PRICING FLOW');
console.log('=================================');

async function debugDynamicPricingFlow() {
  try {
    // Test restaurants with dynamic pricing enabled
    const testRestaurants = [
      {
        id: '01aaadc0-bf00-47bf-b642-5c1fa8a3b912',
        name: 'My Abrast',
        expectedOccupancy: 0.40, // 2/5 tables occupied
        expectedPricing: 'medium traffic (+5%)'
      },
      {
        id: '7ac85a6f-e413-44f8-bf5f-d99be88b54a0',
        name: 'Test Cafe',
        expectedOccupancy: 0.0, // 0/1 tables occupied
        expectedPricing: 'low traffic (-5%)'
      }
    ];

    for (const restaurant of testRestaurants) {
      console.log(`\n🏪 TESTING: ${restaurant.name} (${restaurant.id})`);
      console.log('='.repeat(50));

      // Step 1: Check if dynamic pricing is enabled
      console.log('1️⃣ Checking if dynamic pricing is enabled...');
      const { isDynamicPricingEnabled } = await import('/src/services/dynamicPricingService.ts');
      const isEnabled = await isDynamicPricingEnabled(restaurant.id);
      console.log(`   ✅ Dynamic pricing enabled: ${isEnabled}`);

      if (!isEnabled) {
        console.log('   ⚠️ Skipping - dynamic pricing disabled');
        continue;
      }

      // Step 2: Get current traffic
      console.log('2️⃣ Getting current traffic...');
      const { getCurrentTraffic } = await import('/src/services/trafficService.ts');
      const currentTraffic = await getCurrentTraffic(restaurant.id);
      console.log(`   📊 Current traffic: ${(currentTraffic * 100).toFixed(1)}%`);
      console.log(`   📊 Expected: ${(restaurant.expectedOccupancy * 100).toFixed(1)}%`);

      // Step 3: Get pricing rules
      console.log('3️⃣ Getting pricing rules...');
      const { getPricingRules } = await import('/src/services/dynamicPricingService.ts');
      const pricingRules = await getPricingRules(restaurant.id, null, true);
      console.log('   📋 Pricing rules:', pricingRules);

      // Step 4: Calculate pricing factor
      console.log('4️⃣ Calculating pricing factor...');
      // We need to access the internal function, so let's simulate it
      let expectedFactor = 1.0;
      if (currentTraffic >= (pricingRules?.highTraffic?.threshold || 0.8)) {
        expectedFactor = 1 + Math.abs(pricingRules?.highTraffic?.percentage || 10) / 100;
        console.log(`   🔴 HIGH traffic detected (${(currentTraffic * 100).toFixed(1)}%)`);
      } else if (currentTraffic >= (pricingRules?.mediumTraffic?.threshold || 0.5)) {
        expectedFactor = 1 + Math.abs(pricingRules?.mediumTraffic?.percentage || 5) / 100;
        console.log(`   🟠 MEDIUM traffic detected (${(currentTraffic * 100).toFixed(1)}%)`);
      } else if (currentTraffic <= (pricingRules?.lowTraffic?.threshold || 0.2)) {
        const percentage = Math.abs(pricingRules?.lowTraffic?.percentage || 5);
        if ((pricingRules?.lowTraffic?.percentage || 0) < 0) {
          expectedFactor = 1 - percentage / 100;
          console.log(`   🟢 LOW traffic detected (${(currentTraffic * 100).toFixed(1)}%) - DISCOUNT`);
        } else {
          expectedFactor = 1 + percentage / 100;
          console.log(`   🟢 LOW traffic detected (${(currentTraffic * 100).toFixed(1)}%) - INCREASE`);
        }
      } else {
        console.log(`   🟡 NORMAL traffic detected (${(currentTraffic * 100).toFixed(1)}%)`);
      }
      console.log(`   ⚖️ Expected pricing factor: ${expectedFactor.toFixed(3)}`);

      // Step 5: Test with sample menu items
      console.log('5️⃣ Testing with sample menu items...');
      const sampleItems = [
        {
          id: 'test-food',
          name: 'Test Food Item',
          category: 'food',
          base_price: 10.00,
          price: 10.00
        },
        {
          id: 'test-drink',
          name: 'Test Drink Item',
          category: 'drinks',
          base_price: 5.00,
          price: 5.00
        }
      ];

      const { applyDynamicPricing } = await import('/src/services/dynamicPricingService.ts');
      const pricedItems = await applyDynamicPricing(sampleItems, restaurant.id, true);

      console.log('   📋 Results:');
      pricedItems.forEach(item => {
        const originalPrice = item.base_price || item.price;
        const newPrice = item.price;
        const factor = item.price_factor || 1.0;
        const change = ((newPrice / originalPrice - 1) * 100).toFixed(1);
        
        console.log(`   ${item.category === 'drinks' ? '🍹' : '🍽️'} ${item.name}:`);
        console.log(`      Original: $${originalPrice.toFixed(2)}`);
        console.log(`      New: $${newPrice.toFixed(2)}`);
        console.log(`      Factor: ${factor.toFixed(3)}`);
        console.log(`      Change: ${change > 0 ? '+' : ''}${change}%`);
        console.log(`      Should apply? ${pricingRules?.applyToCategories?.[item.category] ? 'YES' : 'NO'}`);
      });

      // Step 6: Verify expectations
      console.log('6️⃣ Verification:');
      const drinkItem = pricedItems.find(item => item.category === 'drinks');
      const foodItem = pricedItems.find(item => item.category === 'food');
      
      if (drinkItem && pricingRules?.applyToCategories?.drinks) {
        const actualFactor = drinkItem.price_factor || 1.0;
        const factorMatch = Math.abs(actualFactor - expectedFactor) < 0.01;
        console.log(`   🍹 Drinks pricing: ${factorMatch ? '✅ CORRECT' : '❌ INCORRECT'}`);
        if (!factorMatch) {
          console.log(`      Expected factor: ${expectedFactor.toFixed(3)}, Got: ${actualFactor.toFixed(3)}`);
        }
      }
      
      if (foodItem && pricingRules?.applyToCategories?.food) {
        const actualFactor = foodItem.price_factor || 1.0;
        const factorMatch = Math.abs(actualFactor - expectedFactor) < 0.01;
        console.log(`   🍽️ Food pricing: ${factorMatch ? '✅ CORRECT' : '❌ INCORRECT'}`);
        if (!factorMatch) {
          console.log(`      Expected factor: ${expectedFactor.toFixed(3)}, Got: ${actualFactor.toFixed(3)}`);
        }
      }
    }

    console.log('\n🎯 SUMMARY');
    console.log('==========');
    console.log('If you see ❌ INCORRECT above, there might be an issue with:');
    console.log('1. Traffic calculation not matching database state');
    console.log('2. Pricing rules not being applied correctly');
    console.log('3. Category settings not working as expected');
    console.log('\nIf you see ✅ CORRECT, the dynamic pricing is working properly!');

  } catch (error) {
    console.error('❌ Error in debug flow:', error);
  }
}

// Auto-run the debug
debugDynamicPricingFlow();

// Also make it available for manual testing
window.debugDynamicPricingFlow = debugDynamicPricingFlow;

console.log('💡 You can also run: debugDynamicPricingFlow()'); 