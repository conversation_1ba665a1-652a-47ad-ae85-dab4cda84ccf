/**
 * Test Supabase Connection and Dynamic Pricing
 * 
 * This script tests if the app can connect to the real Supabase database
 * and checks the dynamic pricing for Test Cafe
 */

console.log('🔧 TESTING SUPABASE CONNECTION & DYNAMIC PRICING');
console.log('================================================');

async function testSupabaseConnection() {
  try {
    console.log('1️⃣ Checking Supabase client...');
    
    // Import the Supabase client
    const { supabase } = await import('/src/lib/supabase.ts');
    
    console.log('📡 Supabase client:', supabase);
    console.log('📡 Client type:', typeof supabase);
    
    // Test if it's the mock client
    if (supabase.from('test').select === undefined) {
      console.log('❌ Using MOCK Supabase client - no real database connection');
      console.log('💡 Environment variables VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are missing');
      return false;
    }
    
    console.log('2️⃣ Testing database connection...');
    
    // Test a simple query
    const { data, error } = await supabase
      .from('restaurants')
      .select('id, name, dynamic_pricing_enabled')
      .eq('id', '7ac85a6f-e413-44f8-bf5f-d99be88b54a0')
      .single();
    
    if (error) {
      console.log('❌ Database query failed:', error);
      return false;
    }
    
    if (!data) {
      console.log('❌ No data returned from database');
      return false;
    }
    
    console.log('✅ Database connection successful!');
    console.log('🏪 Restaurant data:', data);
    
    console.log('3️⃣ Testing dynamic pricing...');
    
    // Test dynamic pricing
    const { isDynamicPricingEnabled } = await import('/src/services/dynamicPricingService.ts');
    const isPricingEnabled = await isDynamicPricingEnabled('7ac85a6f-e413-44f8-bf5f-d99be88b54a0');
    
    console.log(`⚙️ Dynamic pricing enabled: ${isPricingEnabled}`);
    
    if (!isPricingEnabled) {
      console.log('❌ Dynamic pricing is disabled - this is the issue!');
      return false;
    }
    
    console.log('4️⃣ Testing traffic calculation...');
    
    // Test traffic
    const { getCurrentTraffic } = await import('/src/services/trafficService.ts');
    const currentTraffic = await getCurrentTraffic('7ac85a6f-e413-44f8-bf5f-d99be88b54a0');
    
    console.log(`📊 Current traffic: ${(currentTraffic * 100).toFixed(1)}%`);
    
    // With 100% occupancy, this should be high traffic
    if (currentTraffic >= 0.8) {
      console.log('🔴 HIGH traffic detected - prices should INCREASE by +10%');
    } else if (currentTraffic >= 0.5) {
      console.log('🟠 MEDIUM traffic detected - prices should stay normal');
    } else if (currentTraffic <= 0.3) {
      console.log('🟢 LOW traffic detected - prices should DECREASE by -5%');
    }
    
    console.log('5️⃣ Testing menu pricing...');
    
    // Test with a sample menu item
    const sampleItem = {
      id: 'test-drink',
      name: 'Test Drink',
      category: 'drinks',
      base_price: 6.99,
      price: 6.99
    };
    
    const { applyDynamicPricing } = await import('/src/services/dynamicPricingService.ts');
    const pricedItems = await applyDynamicPricing([sampleItem], '7ac85a6f-e413-44f8-bf5f-d99be88b54a0', true);
    
    const pricedItem = pricedItems[0];
    const originalPrice = sampleItem.base_price;
    const newPrice = pricedItem.price;
    const change = ((newPrice / originalPrice - 1) * 100).toFixed(1);
    
    console.log('🍹 Drink pricing test:');
    console.log(`   Original: $${originalPrice.toFixed(2)}`);
    console.log(`   New: $${newPrice.toFixed(2)}`);
    console.log(`   Change: ${change > 0 ? '+' : ''}${change}%`);
    
    if (currentTraffic >= 0.8 && change > 0) {
      console.log('✅ SUCCESS: High traffic pricing is working!');
      return true;
    } else if (currentTraffic <= 0.3 && change < 0) {
      console.log('✅ SUCCESS: Low traffic pricing is working!');
      return true;
    } else {
      console.log('❌ ISSUE: Dynamic pricing not working as expected');
      console.log(`   Expected: ${currentTraffic >= 0.8 ? 'price increase' : currentTraffic <= 0.3 ? 'price decrease' : 'normal pricing'}`);
      console.log(`   Actual: ${change}% change`);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Error testing Supabase connection:', error);
    return false;
  }
}

// Auto-run the test
testSupabaseConnection().then(success => {
  if (success) {
    console.log('\n🎉 ALL TESTS PASSED - Dynamic pricing is working!');
  } else {
    console.log('\n🚨 TESTS FAILED - Dynamic pricing needs fixing');
    console.log('\n💡 Possible solutions:');
    console.log('1. Check that VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set');
    console.log('2. Restart the development server');
    console.log('3. Clear browser cache and localStorage');
  }
});

// Make it available for manual testing
window.testSupabaseConnection = testSupabaseConnection;

console.log('💡 You can also run: testSupabaseConnection()'); 