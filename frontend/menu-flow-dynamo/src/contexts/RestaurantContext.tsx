import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { getRestaurantId, getRestaurantById, updateRestaurant } from '@/services/restaurantDbService';
import { useAuth } from '@/contexts/AuthContext';
import type { PricingRules } from '@/services/dynamicPricingService';
import { useSearchParams } from 'react-router-dom';

// Define restaurant information type
export interface RestaurantInfo {
  id: string;
  name: string;
  logo_url?: string;
  address?: string;
  contact_email?: string;
  contact_phone?: string;
  description?: string;
  opening_hours?: unknown;
  dynamic_pricing_enabled?: boolean;
  pricing_rules?: PricingRules;
}

// Default restaurant info (used while loading)
const defaultRestaurantInfo: RestaurantInfo = {
  id: '',
  name: 'Restaurant',
  logo_url: '',
  address: '',
  contact_email: '',
  contact_phone: '',
  description: '',
  dynamic_pricing_enabled: false,
  pricing_rules: {
    highTraffic: { threshold: 80, percentage: 10 },
    mediumTraffic: { threshold: 50, percentage: 5 },
    lowTraffic: { threshold: 20, percentage: 0 },
    applyToCategories: { food: true, drinks: false }
  }
};

// Create restaurant context
interface RestaurantContextType {
  restaurantInfo: RestaurantInfo;
  userRestaurants: RestaurantInfo[];
  setRestaurantInfo: (info: Partial<RestaurantInfo>) => void;
  updateRestaurantInDb: (info: Partial<RestaurantInfo>) => Promise<void>;
  selectRestaurant: (restaurantId: string) => void;
  isLoading: boolean;
}

const RestaurantContext = createContext<RestaurantContextType | undefined>(undefined);

// Create restaurant provider
export const RestaurantProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [restaurantInfo, setRestaurantInfoState] = useState<RestaurantInfo>(defaultRestaurantInfo);
  const [userRestaurants, setUserRestaurants] = useState<RestaurantInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const [searchParams] = useSearchParams();

  // Function to update restaurant info state
  const setRestaurantInfo = (info: Partial<RestaurantInfo>) => {
    setRestaurantInfoState(prev => {
      const updated = { ...prev, ...info };
      
      // Save to localStorage for persistence
      if (updated.id) {
        localStorage.setItem('selectedRestaurantId', updated.id);
      }
      
      return updated;
    });
  };

  // Function to update restaurant in database
  const updateRestaurantInDb = async (info: Partial<RestaurantInfo>) => {
    if (!restaurantInfo.id) {
      console.error('Cannot update restaurant: No restaurant ID');
      return;
    }

    try {
      await updateRestaurant(restaurantInfo.id, info);
      setRestaurantInfo(info);
    } catch (error) {
      console.error('Error updating restaurant:', error);
    }
  };

  // Function to select a restaurant from the dropdown
  const selectRestaurant = async (restaurantId: string) => {
    if (!restaurantId) {
      console.error('Cannot select restaurant: No restaurant ID provided');
      return;
    }

    try {
      setIsLoading(true);
      
      // Find the restaurant in the user's restaurants
      const selectedRestaurant = userRestaurants.find(r => r.id === restaurantId);
      
      if (selectedRestaurant) {
        // If we already have basic info, use it immediately
        setRestaurantInfoState(selectedRestaurant);
        
        // Save selection to localStorage
        localStorage.setItem('selectedRestaurantId', restaurantId);
        
        // Also fetch full details from the database
        const fullDetails = await getRestaurantById(restaurantId);
        
        if (fullDetails) {
          setRestaurantInfoState(prev => ({
            ...prev,
            ...fullDetails
          }));
        }
      } else {
        // If not found in user's restaurants, fetch from database
        const restaurant = await getRestaurantById(restaurantId);
        
        if (restaurant) {
          setRestaurantInfoState(restaurant);
          localStorage.setItem('selectedRestaurantId', restaurantId);
        } else {
          console.error(`Restaurant with ID ${restaurantId} not found`);
        }
      }
    } catch (error) {
      console.error('Error selecting restaurant:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Effect to fetch restaurant info when user or table ID changes
  useEffect(() => {
    // Fetch restaurant info from database
    const fetchRestaurantInfo = async () => {
      try {
        setIsLoading(true);
        
        // Check if there's a table ID in the URL
        const tableId = searchParams.get('table');
        
        // For customer views (with table ID), allow unauthenticated access
        // For admin views (without table ID), require authentication
        if (!user && !tableId) {
          console.warn('User not authenticated yet, waiting for auth state...');
          setIsLoading(false);
          return;
        }

        if (tableId) {
          try {
            // If there's a table ID, get the restaurant for that table
            const { data, error } = await supabase
              .from('restaurant_tables')
              .select('restaurant_id')
              .eq('id', tableId)
              .single();

            if (error) {
              console.error('Error fetching table:', error);
            } else if (data && data.restaurant_id) {
              // If we found the restaurant ID for this table, select it
              const restaurant = await getRestaurantById(data.restaurant_id);
              
              if (restaurant) {
                setRestaurantInfo({
                  id: restaurant.id,
                  name: restaurant.name,
                  logo_url: restaurant.logo_url,
                  address: restaurant.address,
                  contact_email: restaurant.contact_email,
                  contact_phone: restaurant.contact_phone,
                  description: restaurant.description,
                  opening_hours: restaurant.opening_hours,
                  dynamic_pricing_enabled: restaurant.dynamic_pricing_enabled,
                  pricing_rules: restaurant.pricing_rules
                });
                localStorage.setItem('selectedRestaurantId', data.restaurant_id);
                setIsLoading(false);
                return;
              }
            }
          } catch (err) {
            console.error('Error processing table ID:', err);
          }
        }
        
        // If no user is authenticated and no table ID, we can't proceed
        if (!user) {
          console.warn('No authenticated user and no table ID provided');
          setIsLoading(false);
          return;
        }
        
        // Debug logging
        console.log('User ID:', user.id);
        console.log('Current restaurantInfo:', restaurantInfo);
        
        // Try to get restaurant ID from user context
        console.log('Getting restaurant info with tableId:', tableId);
        let restaurantId: string | null = null;
        
        // Check for previously selected restaurant ID in localStorage
        const savedRestaurantId = localStorage.getItem('selectedRestaurantId');
        const savedUserId = localStorage.getItem('lastUserId');
        
        // Clear saved restaurant if user changed
        if (savedUserId !== user.id) {
          console.log('User changed, clearing saved restaurant ID');
          localStorage.removeItem('selectedRestaurantId');
        }
        
        // Save current user ID
        localStorage.setItem('lastUserId', user.id);
        
        // If we have a saved restaurant ID and the user hasn't changed, try to use it
        if (savedRestaurantId && savedUserId === user.id) {
          console.log('Using saved restaurant ID:', savedRestaurantId);
          restaurantId = savedRestaurantId;
        } else {
          // Get the correct business for Restaurant Management app module
          console.log('🔍 Querying business app modules for restaurant management...');
          const { data: businessAppModules, error: businessAppError } = await supabase
            .from('business_app_modules')
            .select(`
              business_id,
              businesses!inner(id, name, user_id),
              app_modules!inner(name, display_name)
            `)
            .eq('businesses.user_id', user.id)
            .eq('app_modules.name', 'restaurant_management')
            .eq('is_active', true);

          if (businessAppError) {
            console.error('❌ Error fetching business app modules:', businessAppError);
            console.error('This might indicate the app_modules architecture is not properly set up');
            setIsLoading(false);
            return;
          }

          console.log('✅ Successfully queried business app modules:', businessAppModules?.length || 0, 'found');
          
          // Find the business with the most restaurant data (menus, items, tables)
          let selectedBusinessId = null;

          if (businessAppModules && businessAppModules.length > 0) {
            console.log(`Found ${businessAppModules.length} businesses with restaurant management enabled`);

            // Optimize: Get menu counts for all businesses in a single query
            const businessIds = businessAppModules.map(bm => bm.business_id);
            const { data: menuCounts } = await supabase
              .from('menus')
              .select('restaurant_id')
              .in('restaurant_id', businessIds);

            // Count menus per business
            const menuCountMap = menuCounts?.reduce((acc, menu) => {
              acc[menu.restaurant_id] = (acc[menu.restaurant_id] || 0) + 1;
              return acc;
            }, {} as Record<string, number>) || {};

            // Find business with most menus
            let maxMenus = 0;
            for (const businessModule of businessAppModules) {
              const businessId = businessModule.business_id;
              const menuCount = menuCountMap[businessId] || 0;

              console.log(`Business ${businessModule.businesses.name} has ${menuCount} menus`);

              if (menuCount > maxMenus) {
                maxMenus = menuCount;
                selectedBusinessId = businessId;
                console.log(`Selected business: ${businessModule.businesses.name} (${businessId}) with ${menuCount} menus`);
              }
            }

            // If no business found with menu data, use the first one
            if (!selectedBusinessId && businessAppModules.length > 0) {
              selectedBusinessId = businessAppModules[0].business_id;
              console.log(`Fallback to first business: ${businessAppModules[0].businesses.name}`);
            }
          }
          
          if (selectedBusinessId) {
            restaurantId = selectedBusinessId;
            
            // Get all restaurants for the dropdown (from restaurants_unified table)
            const { data: userRestaurants, error: userRestaurantsError } = await supabase
              .from('restaurants_unified')
              .select('*')
              .eq('user_id', user.id)
              .order('created_at', { ascending: false });
              
            if (!userRestaurantsError && userRestaurants) {
              setUserRestaurants(userRestaurants.map(r => ({
                id: r.id,
                name: r.name,
                address: r.address,
                contact_email: r.contact_email,
                logo_url: r.logo_url
              })));
            }
                      } else {
            // No businesses found with restaurant management enabled - try fallback
            console.warn('⚠️ No businesses found with restaurant management app module enabled');
            console.log('🔄 Attempting fallback to restaurants_unified view...');

            // Fallback: Try to get restaurants directly from restaurants_unified view
            const { data: fallbackRestaurants, error: fallbackError } = await supabase
              .from('restaurants_unified')
              .select('*')
              .eq('user_id', user.id)
              .order('created_at', { ascending: false });

            if (!fallbackError && fallbackRestaurants && fallbackRestaurants.length > 0) {
              console.log('✅ Fallback successful: Found', fallbackRestaurants.length, 'restaurants');
              restaurantId = fallbackRestaurants[0].id;

              setUserRestaurants(fallbackRestaurants.map(r => ({
                id: r.id,
                name: r.name,
                address: r.address,
                contact_email: r.contact_email,
                logo_url: r.logo_url
              })));
            } else {
              console.error('❌ Fallback failed - no restaurants found');
              setIsLoading(false);
              return;
            }
          }
        }

        console.log('Using restaurant ID:', restaurantId);

        // CRITICAL: Ensure we have a valid restaurant ID before proceeding
        if (!restaurantId) {
          console.error('No restaurant ID available after all attempts');
          setIsLoading(false);
          return;
        }

        try {
          // Fetch restaurant details from the database
          const restaurant = await getRestaurantById(restaurantId);

          if (restaurant) {
            console.log(`Setting restaurant info for: ${restaurant.name} (ID: ${restaurant.id})`);
            setRestaurantInfo({
              id: restaurant.id,
              name: restaurant.name,
              logo_url: restaurant.logo_url,
              address: restaurant.address,
              contact_email: restaurant.contact_email,
              contact_phone: restaurant.contact_phone,
              description: restaurant.description,
              opening_hours: restaurant.opening_hours,
              dynamic_pricing_enabled: restaurant.dynamic_pricing_enabled,
              pricing_rules: restaurant.pricing_rules
            });
            
            // Save to localStorage for persistence
            localStorage.setItem('selectedRestaurantId', restaurant.id);
          } else {
            // If no restaurant found, use the ID we already have with default values
            console.log(`No restaurant details found for ID: ${restaurantId}, using default values`);
            setRestaurantInfo({
              id: restaurantId,
              name: 'My Restaurant',
              address: '123 Main St',
              contact_email: user?.email || '<EMAIL>',
              dynamic_pricing_enabled: defaultRestaurantInfo.dynamic_pricing_enabled,
              pricing_rules: defaultRestaurantInfo.pricing_rules
            });
            
            // Save to localStorage for persistence
            localStorage.setItem('selectedRestaurantId', restaurantId);
          }
        } catch (restaurantError) {
          console.error('Error fetching restaurant details:', restaurantError);
          // Still set the restaurant ID we have, even if details fetch failed
          setRestaurantInfo({
            id: restaurantId,
            name: 'My Restaurant',
            address: '123 Main St',
            contact_email: user?.email || '<EMAIL>',
          });
          
          // Save to localStorage for persistence
          localStorage.setItem('selectedRestaurantId', restaurantId);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching restaurant info:', error);
        setIsLoading(false);
      }
    };

    // Fetch restaurant info when component mounts or when user or table ID changes
    fetchRestaurantInfo();
  }, [user, searchParams]);

  return (
    <RestaurantContext.Provider
      value={{
        restaurantInfo,
        userRestaurants,
        setRestaurantInfo,
        updateRestaurantInDb,
        selectRestaurant,
        isLoading
      }}
    >
      {children}
    </RestaurantContext.Provider>
  );
};

// Create hook for using restaurant context
export const useRestaurant = () => {
  const context = useContext(RestaurantContext);
  if (context === undefined) {
    throw new Error('useRestaurant must be used within a RestaurantProvider');
  }
  return context;
};

export default RestaurantProvider;
