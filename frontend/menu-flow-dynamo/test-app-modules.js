// Test script to verify app_modules architecture is working
// Run this in the browser console to test the database queries

async function testAppModulesArchitecture() {
  console.log('🧪 Testing App Modules Architecture...');
  
  try {
    // Test 1: Check if app_modules table exists and has data
    console.log('\n1️⃣ Testing app_modules table...');
    const { data: appModules, error: appModulesError } = await supabase
      .from('app_modules')
      .select('*')
      .order('name');
    
    if (appModulesError) {
      console.error('❌ app_modules query failed:', appModulesError);
      return;
    }
    
    console.log('✅ app_modules found:', appModules.length);
    appModules.forEach(module => {
      console.log(`  - ${module.name}: ${module.display_name}`);
    });
    
    // Test 2: Check business_app_modules for current user
    console.log('\n2️⃣ Testing business_app_modules for current user...');
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.error('❌ No authenticated user found');
      return;
    }
    
    console.log('👤 Current user:', user.id);
    
    const { data: businessAppModules, error: businessAppError } = await supabase
      .from('business_app_modules')
      .select(`
        business_id,
        is_active,
        businesses!inner(id, name, user_id),
        app_modules!inner(name, display_name)
      `)
      .eq('businesses.user_id', user.id);
    
    if (businessAppError) {
      console.error('❌ business_app_modules query failed:', businessAppError);
      return;
    }
    
    console.log('✅ business_app_modules found:', businessAppModules.length);
    businessAppModules.forEach(bam => {
      console.log(`  - Business: ${bam.businesses.name}`);
      console.log(`    App: ${bam.app_modules.display_name} (${bam.is_active ? 'active' : 'inactive'})`);
    });
    
    // Test 3: Check restaurants_unified view
    console.log('\n3️⃣ Testing restaurants_unified view...');
    const { data: restaurants, error: restaurantsError } = await supabase
      .from('restaurants_unified')
      .select('id, name, user_id')
      .eq('user_id', user.id);
    
    if (restaurantsError) {
      console.error('❌ restaurants_unified query failed:', restaurantsError);
      return;
    }
    
    console.log('✅ restaurants_unified found:', restaurants.length);
    restaurants.forEach(restaurant => {
      console.log(`  - ${restaurant.name} (${restaurant.id})`);
    });
    
    // Test 4: Check menu data for restaurants
    console.log('\n4️⃣ Testing menu data...');
    for (const restaurant of restaurants) {
      const { data: menus, error: menusError } = await supabase
        .from('menus')
        .select('id, name')
        .eq('restaurant_id', restaurant.id);
      
      if (!menusError && menus) {
        console.log(`  - ${restaurant.name}: ${menus.length} menus`);
      }
    }
    
    console.log('\n🎉 App Modules Architecture Test Complete!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Export for use in browser console
window.testAppModulesArchitecture = testAppModulesArchitecture;

console.log('🧪 App Modules Architecture Test loaded!');
console.log('Run: testAppModulesArchitecture() in the console to test');
