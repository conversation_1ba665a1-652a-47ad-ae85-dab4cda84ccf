# App Context Implementation Guide

## Overview

The SME Analytica system now has a comprehensive app context separation system that allows proper identification and data isolation between the three main applications:

1. **SME Analytica Mobile** (`sme_analytica`) - Core business analytics platform
2. **Restaurant Management System** (`restaurant_mgmt`) - Restaurant operations and ordering  
3. **Connecto** (`connecto`) - AI Voice Receptionist Platform

## Quick Start

### 1. Environment Setup

#### Mobile App (SME Analytica)
```bash
# Copy environment template
cp mobile/.env.example mobile/.env.local

# Set application identity
EXPO_PUBLIC_APPLICATION_NAME=sme_analytica
EXPO_PUBLIC_APPLICATION_ID=73e19dba-25df-4c45-949d-e42f19df912a
```

#### Restaurant Management System
```bash
# Copy environment template  
cp frontend/menu-flow-dynamo/.env.example frontend/menu-flow-dynamo/.env.local

# Set application identity
VITE_APPLICATION_NAME=restaurant_mgmt
VITE_APPLICATION_ID=4a631768-6241-4d04-9066-932cd22350e5
```

#### Backend API
```bash
# Set in environment or .env file
APPLICATION_NAME=sme_analytica_backend
APPLICATION_ID=backend-service
```

### 2. Initialize App Context

#### Mobile App Usage
```typescript
import { appContextService } from '@/services/appContextService';

// Initialize when user logs in
const userId = "user-uuid";
await appContextService.initializeAppContext(userId);

// Track user events
await appContextService.trackEvent('screen_view', {
  screenName: 'Dashboard',
  feature: 'analytics'
});

// Record metrics
await appContextService.recordAnalytics({
  metricName: 'daily_active_users',
  metricValue: 1,
  metricType: 'counter'
});
```

#### Restaurant System Usage
```typescript
import { appContextService } from '@/services/appContextService';

// Initialize with restaurant context
const userId = "user-uuid";
const restaurantId = "restaurant-uuid";
await appContextService.initializeAppContext(userId, restaurantId);

// Track restaurant operations
await appContextService.trackRestaurantEvent('order_created', restaurantId, {
  orderTotal: 25.99,
  itemCount: 3,
  paymentMethod: 'card'
});

// Track dynamic pricing
await appContextService.trackDynamicPricingEvent('price_adjustment', restaurantId, {
  itemId: 'item-uuid',
  oldPrice: 12.99,
  newPrice: 14.99,
  adjustment: 0.15,
  reason: 'high_traffic'
});
```

## Database Schema

### Core Tables

#### `app_sessions`
Tracks user sessions per application:
```sql
- id: UUID (Primary Key)
- user_id: UUID (Foreign Key to profiles)
- application_name: TEXT (e.g., 'sme_analytica')
- application_id: UUID
- session_id: TEXT (Unique)
- device_id: TEXT
- session_start: TIMESTAMP
- session_end: TIMESTAMP (NULL during active session)
- session_data: JSONB (Custom session data)
- ip_address: INET
- user_agent: TEXT
```

#### `user_app_preferences`
User preferences isolated by application:
```sql
- id: UUID (Primary Key)
- user_id: UUID (Foreign Key to profiles)
- application_name: TEXT
- application_id: UUID
- preferences: JSONB (App-specific settings)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### `app_analytics`
Metrics and analytics separated by application:
```sql
- id: UUID (Primary Key)
- application_name: TEXT
- application_id: UUID
- user_id: UUID (Foreign Key to profiles, nullable)
- metric_name: TEXT
- metric_value: NUMERIC
- metric_type: TEXT (counter, gauge, histogram)
- dimensions: JSONB (Additional context)
- tags: JSONB (Key-value tags)
- recorded_at: TIMESTAMP
```

#### `app_events`
Detailed event tracking with app context:
```sql
- id: UUID (Primary Key)
- application_name: TEXT
- application_id: UUID
- user_id: UUID (Foreign Key to profiles, nullable)
- session_id: TEXT
- event_name: TEXT
- event_type: TEXT (user_action, system_event, error)
- event_data: JSONB (Event details)
- screen_name: TEXT
- feature_name: TEXT
- recorded_at: TIMESTAMP
```

### Helper Functions

#### `start_app_session()`
```sql
SELECT start_app_session(
  p_user_id := 'user-uuid',
  p_application_name := 'sme_analytica',
  p_application_id := 'app-uuid',
  p_session_id := 'session-id',
  p_device_id := 'device-id',
  p_session_data := '{"platform": "mobile"}'::jsonb
);
```

#### `end_app_session()`
```sql
SELECT end_app_session(p_session_id := 'session-id');
```

#### `track_app_event()`
```sql
SELECT track_app_event(
  p_application_name := 'restaurant_mgmt',
  p_event_name := 'order_created',
  p_user_id := 'user-uuid',
  p_event_data := '{"orderTotal": 25.99, "itemCount": 3}'::jsonb,
  p_feature_name := 'ordering'
);
```

## Configuration Files

### Mobile App Configuration (`mobile/config/app.ts`)
```typescript
export const appConfig: AppConfig = {
  applicationName: 'sme_analytica',
  applicationId: '73e19dba-25df-4c45-949d-e42f19df912a',
  displayName: 'SME Analytica',
  description: 'Business Analytics Platform for Small and Medium Enterprises',
  // ... other config
};
```

### Restaurant System Configuration (`frontend/menu-flow-dynamo/src/config/app.ts`)
```typescript
export const appConfig: AppConfig = {
  applicationName: 'restaurant_mgmt',
  applicationId: '4a631768-6241-4d04-9066-932cd22350e5',
  displayName: 'Restaurant Management System',
  description: 'Complete restaurant operations and ordering platform',
  // ... other config
};
```

## Common Patterns

### 1. Event Tracking
```typescript
// User interaction events
await appContextService.trackEvent('button_click', {
  buttonName: 'save_preferences',
  screenName: 'settings',
  timestamp: new Date().toISOString()
});

// Feature usage events
await appContextService.trackEvent('feature_used', {
  featureName: 'dynamic_pricing',
  action: 'enabled',
  userId: currentUser.id
});

// Error tracking
await appContextService.trackEvent('error_occurred', {
  errorType: 'api_error',
  errorMessage: 'Failed to save data',
  stackTrace: error.stack
}, 'error');
```

### 2. Analytics Recording
```typescript
// Performance metrics
await appContextService.recordAnalytics({
  metricName: 'page_load_time',
  metricValue: 1500, // milliseconds
  metricType: 'gauge',
  dimensions: { page: 'dashboard' }
});

// Business metrics
await appContextService.recordAnalytics({
  metricName: 'revenue_generated',
  metricValue: 150.75,
  metricType: 'counter',
  tags: { restaurant: restaurantId, currency: 'USD' }
});

// Usage metrics
await appContextService.recordAnalytics({
  metricName: 'api_calls',
  metricValue: 1,
  metricType: 'counter',
  dimensions: { endpoint: '/api/orders', method: 'POST' }
});
```

### 3. Preferences Management
```typescript
// Get user preferences
const preferences = await appContextService.getUserPreferences();

// Save updated preferences
await appContextService.saveUserPreferences({
  theme: 'dark',
  language: 'en',
  notifications: {
    email: true,
    push: false
  },
  dashboardLayout: 'compact'
});
```

## Integration Steps

### 1. Update Existing Services

#### Before (without app context):
```typescript
// Old analytics call
analytics.track('user_login', { method: 'email' });
```

#### After (with app context):
```typescript
// New analytics call with app context
await appContextService.trackEvent('user_login', { method: 'email' });
```

### 2. Update Component Initialization

#### Before:
```typescript
useEffect(() => {
  // Initialize component
  loadUserData();
}, []);
```

#### After:
```typescript
useEffect(() => {
  // Initialize with app context
  appContextService.initializeAppContext(userId).then(() => {
    loadUserData();
  });
}, [userId]);
```

### 3. Update Database Queries

#### Before:
```typescript
// Generic analytics query
const { data } = await supabase
  .from('analytics')
  .select('*')
  .eq('user_id', userId);
```

#### After:
```typescript
// App-specific analytics query
const analytics = await appContextService.getAppAnalytics(
  ['daily_active_users', 'feature_usage'],
  '2025-01-01',
  '2025-01-31'
);
```

## Testing

### 1. Verify App Context Initialization
```typescript
// Test app context is properly set
const context = appContextService.getAppContext();
expect(context.applicationName).toBe('sme_analytica');
expect(context.applicationId).toBe('73e19dba-25df-4c45-949d-e42f19df912a');
```

### 2. Test Event Tracking
```typescript
// Test event tracking
const eventId = await appContextService.trackEvent('test_event', { data: 'test' });
expect(eventId).toBeTruthy();

// Verify in database
const events = await supabase
  .from('app_events')
  .select('*')
  .eq('application_name', 'sme_analytica')
  .eq('event_name', 'test_event');
expect(events.data).toHaveLength(1);
```

### 3. Test Data Isolation
```typescript
// Test that mobile app can't see restaurant data
const mobileAnalytics = await appContextService.getAppAnalytics();
const restaurantAnalytics = await restaurantAppContext.getAppAnalytics();

// Should be completely separate
expect(mobileAnalytics).not.toContain(restaurantAnalytics);
```

## Security Considerations

### 1. Row Level Security (RLS)
All app context tables have RLS policies that ensure:
- Users can only access their own data
- App data is properly isolated
- Service role has full access for system operations

### 2. Data Validation
```typescript
// Always validate app context before operations
if (!appContextService.isInitialized()) {
  throw new Error('App context not initialized');
}

const context = appContextService.getAppContext();
if (!context.applicationName) {
  throw new Error('Invalid application name');
}
```

### 3. Environment Variables
```bash
# Never expose sensitive app IDs in client-side code
# Use environment variables for configuration
EXPO_PUBLIC_APPLICATION_NAME=sme_analytica
EXPO_PUBLIC_APPLICATION_ID=73e19dba-25df-4c45-949d-e42f19df912a
```

## Troubleshooting

### Common Issues

#### 1. App Context Not Initialized
```typescript
// Check if context is initialized
if (!appContextService.isInitialized()) {
  console.error('App context not initialized. Call initializeAppContext() first.');
  await appContextService.initializeAppContext(userId);
}
```

#### 2. Environment Variables Not Loaded
```typescript
// Check environment variables
const config = appContextService.getAppConfig();
console.log('App Config:', config);

// Verify in browser dev tools or React Native debugger
console.log('APPLICATION_NAME:', process.env.EXPO_PUBLIC_APPLICATION_NAME);
```

#### 3. Database Permission Issues
```sql
-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'app_sessions';

-- Verify user permissions
SELECT auth.uid(), auth.role();
```

## Next Steps

1. **Integration**: Update existing services to use app context
2. **Analytics Dashboard**: Build app-specific analytics views
3. **Testing**: Comprehensive testing of app separation
4. **Documentation**: Update API documentation with app context patterns
5. **Monitoring**: Set up alerts for app-specific metrics

## Support

For questions or issues:
1. Check the troubleshooting section above
2. Review database logs in Supabase dashboard
3. Check console logs for app context initialization
4. Verify environment variables are properly set
5. Test with the provided examples

---

**Last Updated**: 2025-01-16  
**Implementation Status**: ✅ **COMPLETE**  
**Next Phase**: Integration & Testing