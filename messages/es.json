{"nav": {"home": "<PERSON><PERSON>o", "platform": "Visión General de la Plataforma", "modules": "<PERSON><PERSON><PERSON><PERSON>", "ai": "IA y Automatización", "integration": "Integraciones", "business": "Para Empresas", "about": "Acerca de Nosotros", "developer": "Documentación para Desarrolladores", "language": "Idioma"}, "home": {"title": "Documentación de SME Analytica", "subtitle": "La guía definitiva para inteligencia empresarial impulsada por IA para pequeñas y medianas empresas", "description": "Documentación integral para propietarios de empresas, desarrolladores, VCs e inversores para entender qué es SME Analytica, qué construimos y cómo usar nuestro sistema.", "getStarted": "Comenzar", "viewDemo": "Ver Demo", "mission": "Democratizando la analítica de IA para cada pequeña empresa", "vision": "Convertirnos en la plataforma líder de inteligencia empresarial de IA que empodera a las PYMEs para competir con insights de nivel empresarial", "whoItsFor": {"title": "Para Quién es SME Analytica", "sme": {"title": "Pequeñas y Medianas Empresas", "description": "Restaurantes, tiendas minoristas, clínicas, salones y empresas de servicios"}, "hospitality": {"title": "Industria de Hospitalidad", "description": "Hoteles, restaurantes, cafeterías y lugares de entretenimiento"}, "retail": {"title": "Retail y E-commerce", "description": "Tiendas físicas, tiendas en línea y negocios de retail híbridos"}, "healthcare": {"title": "Salud y Bienestar", "description": "Clínicas, consultorios dentales, spas y centros de fitness"}}}, "platform": {"title": "Visión General de la Plataforma", "whatIs": {"title": "¿Qué es SME Analytica?", "description": "SME Analytica es una plataforma de Inteligencia Empresarial de IA de próxima generación que ayuda a las pequeñas y medianas empresas a tomar decisiones basadas en datos a través de análisis avanzados, análisis de sentimientos y optimización de precios dinámicos."}, "benefits": {"title": "Benef<PERSON><PERSON>", "realtime": {"title": "Análisis en Tiempo Real", "description": "Obtén insights instantáneos sobre el rendimiento de tu negocio con actualizaciones de datos en vivo y monitoreo"}, "ai": {"title": "Insights Impulsados por IA", "description": "Algoritmos avanzados de aprendizaje automático analizan tus datos para proporcionar recomendaciones accionables"}, "pricing": {"title": "<PERSON><PERSON><PERSON>", "description": "Optimiza tu estrategia de precios con pronósticos de demanda impulsados por IA y análisis de competidores"}, "integration": {"title": "Integración Perfecta", "description": "Conecta con sistemas POS existentes, procesadores de pagos y herramientas empresariales"}}, "techStack": {"title": "Stack <PERSON>ógico", "backend": "Backend de Python FastAPI con integración avanzada de IA", "frontend": "Aplicación móvil React Native y aplicaciones web Next.js", "database": "Supabase con PostgreSQL para gestión robusta de datos", "ai": "Integración OpenRouter con modelos <PERSON>, <PERSON>, DeepSeek y Perplexity", "infrastructure": "Infraestructura en la nube AWS con contenedorización Docker"}}, "modules": {"title": "Módulos de SME Analytica", "menuflow": {"title": "MenuFlow (Gestión de Restaurantes)", "description": "Sistema completo de gestión de restaurantes con precios dinámicos, pedidos basados en QR e integración POS", "features": {"pricing": "Sistema de precios dinámicos impulsado por IA", "qr": "Pedidos de menú en vivo basados en código QR", "tracking": "Seguimiento de tráfico y pedidos en tiempo real", "pos": "Integración perfecta del sistema POS", "analytics": "Análisis integral de restaurantes"}, "link": "restaurants.smeanalytica.dev"}, "connecto": {"title": "Connecto (Recepcionista de Voz IA)", "description": "Asistente de voz inteligente que maneja llamadas de clientes, reservas y consultas", "features": {"voice": "Interacción de voz en lenguaje natural", "booking": "Reservas automáticas de citas y reservaciones", "multilingual": "Soporte para múltiples idiomas", "integration": "Integración con sistemas telefónicos existentes", "analytics": "Análisis e <PERSON> de llamadas"}, "status": "En Construcción"}, "mobile": {"title": "App SME Analytics", "description": "Aplicación móvil integral para análisis empresarial e insights", "features": {"sales": "Análisis de rendimiento de ventas", "sentiment": "Seguimiento de sentimientos de clientes", "competitor": "Benchmarking de competidores", "kpi": "Visualizaciones de KPI en tiempo real", "insights": "Insights empresariales generados por IA"}}, "ticketing": {"title": "Motor de Venta de Entradas para Eventos", "description": "Sistema de venta de entradas con precios dinámicos para eventos, clubes y lugares de entretenimiento", "features": {"dynamic": "Precios dinámicos en tiempo real", "events": "Gestión y promoción de eventos", "analytics": "Análisis de ventas de entradas", "integration": "Integración de gestión de lugares"}, "status": "<PERSON><PERSON><PERSON><PERSON>"}}, "ai": {"title": "IA y Automatización", "description": "Cómo SME Analytica aprovecha la inteligencia artificial para proporcionar insights empresariales inteligentes", "models": {"title": "Modelos de IA Utilizados", "llm": "<PERSON><PERSON> de Lenguaje <PERSON> (GPT-<PERSON>, <PERSON>, DeepSeek)", "ml": "Algoritmos de Aprendizaje Automático para análisis predictivo", "nlp": "Procesamiento de Lenguaje Natural para análisis de sentimientos", "computer_vision": "Visión por computadora para análisis de imágenes y videos"}, "dataSources": {"title": "Fuentes de Datos", "google": "API de Google Places para datos empresariales", "tripadvisor": "API de TripAdvisor para reseñas y calificaciones", "yelp": "API de Yelp para comentarios de clientes", "internal": "Datos empresariales internos y transacciones"}, "privacy": {"title": "Privacidad de Datos y Seguridad", "encryption": "Cifrado de extremo a extremo para toda transmisión de datos", "compliance": "Manejo de datos compatible con GDPR y CCPA", "storage": "Almacenamiento seguro en la nube con redundancia de respaldo", "access": "Control de acceso basado en roles y autenticación"}}, "integration": {"title": "Documentación de Integración", "pos": {"title": "Integración de Sistemas POS", "description": "Conecta SME Analytica con sistemas de punto de venta populares", "supported": "Sistemas soportados: Square, Toast, Clover, Shopify POS", "features": "Sincronización de datos de ventas en tiempo real, gestión de inventario, integración de datos de clientes"}, "supabase": {"title": "Esquema de Supabase", "description": "Arquitectura de base de datos y relaciones de tablas", "tables": "Tablas principales: usuarios, empresas, análisis, transacciones, insights"}, "webhooks": {"title": "Webhooks y APIs", "description": "APIs RESTful e integración de webhooks para servicios de terceros", "endpoints": "Endpoints de autenticación, análisis, reportes y notificaciones"}}, "business": {"title": "Para Empresas", "useCases": {"title": "Casos de Uso por Industria", "restaurant": "Optimización de restaurantes, ingeniería de menús, análisis de flujo de clientes", "retail": "Optimización de inventario, análisis de comportamiento de clientes, estrategias de precios", "healthcare": "Optimización de flujo de pacientes, programación de citas, análisis de servicios", "hospitality": "Optimización de ocupación, análisis de experiencia de huéspedes, gestión de ingresos"}, "onboarding": {"title": "<PERSON><PERSON><PERSON>", "step1": "Regístrate para una cuenta de SME Analytica", "step2": "Completa el perfil empresarial y verificación", "step3": "Conecta tus herramientas empresariales existentes", "step4": "Comienza a analizar con insights impulsados por IA"}, "demo": {"title": "Solicitar una Demo", "description": "Ve SME Analytica en acción con una demo personalizada para tu negocio", "contact": "Contacta a nuestro equipo para una consulta y demo gratuita"}}, "about": {"title": "Acerca de SME Analytica", "company": {"title": "Visión General de la Empresa", "description": "SME Analytica fue fundada con la misión de democratizar la inteligencia empresarial impulsada por IA para pequeñas y medianas empresas en todo el mundo."}, "timeline": {"title": "Cronología de la Empresa", "2023": "Desarrollo de plataforma e integración de IA", "2024": "Lanzamiento de aplicación móvil y despliegue del módulo de restaurantes", "2025": "Expansión multi-industria y características avanzadas"}, "contact": {"title": "Información de Contacto", "email": "<EMAIL>", "twitter": "@smeanalytica", "website": "smeanalytica.dev"}}, "developer": {"title": "Documentación para Desarrolladores", "api": {"title": "Referencia de API", "description": "Documentación completa de API para integrar con SME Analytica", "authentication": "Sistema de autenticación basado en JWT", "endpoints": "Endpoints RESTful para todas las características de la plataforma"}, "webhooks": {"title": "Eventos de Webhook", "description": "Notificaciones de eventos en tiempo real para cambios en datos empresariales", "events": "analysis_complete, pricing_update, alert_triggered"}, "sdk": {"title": "Kits de Desarrollo de Software", "javascript": "SDK de JavaScript/TypeScript para integración web", "python": "SDK de Python para integración backend", "mobile": "Componentes React Native para aplicaciones móviles"}}}